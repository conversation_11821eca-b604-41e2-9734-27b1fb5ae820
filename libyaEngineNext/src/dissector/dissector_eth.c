#include "yaEngineNext/nxt_engine.h"
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#define PROTO_NAME "eth"

static
int ether_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    precord_put(precord, "dst",  bytes,    nxt_mbuf_get_raw(mbuf, 0), 6);
    precord_put(precord, "src",  bytes,    nxt_mbuf_get_raw(mbuf, 6), 6);

    uint16_t type = nxt_mbuf_get_uint16_ntoh(mbuf, 12);
    precord_put(precord, "type", uinteger, type);

    nxt_handoff_set_key_of_number(engine, type);
    return 14;
}

static
int ether_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PSCHEMA_TEMPLATE_NONE, PROTO_NAME, "ethernet");
     pschema_register_field(pschema, "dst",  YA_FT_ETHER,  "dst mac address");
     pschema_register_field(pschema, "src",  YA_FT_ETHER,  "src mac address");
     pschema_register_field_ex(pschema, "type", YA_FT_UINT16, "ether type", YA_DISPLAY_BASE_HEX);

     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "eth",
    .schemaRegFun = ether_schema_reg,
    .dissectFun   = ether_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(eth)
{
    nxt_dissector_register(&gDissectorDef);
}
