#ifndef NXT_UTIL_H
#define NXT_UTIL_H

#include "nxt_export.h"
#include <stdint.h>

typedef struct nxt_Tuple5Ipv4   nxt_tuple_5_ipv4_t;
typedef struct ProtoRecord      precord_t;

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

NXT_EXPORT int      nxt_util_show_precord(precord_t *precord);
NXT_EXPORT void     nxt_util_ipv4_to_str(const uint8_t *address, char *buf, const int bufLen);
NXT_EXPORT void     nxt_util_t5ipv4_to_str(nxt_tuple_5_ipv4_t* t5, char *buff, int buffLen);
NXT_EXPORT uint32_t nxt_utils_current_time_ms();

#ifdef __cplusplus
}
#endif

#endif /* NXT_UTIL_H */
