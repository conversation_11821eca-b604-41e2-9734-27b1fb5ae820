load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_h225ras.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_trailer_rt.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_rtp.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_q931.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_h245.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_smtp.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_mpls.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_vlan.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_story.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_h225.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_trailer_hwzzeth.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_sdx.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_dns.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_gtp_u.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_udp.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_snmp.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_trailer_trailer_hwzz.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_tpkt.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_h235.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_sip.so
INFO: error: dissector [] not found.

offline mode: read pcap from file pcaps/H225_H245.pcap
dissector_h225ras
H225RAS: Attempting to decode 127 bytes of data
H225RAS: First few bytes: 26 90 5f 59
H225RAS: BER decode failed or invalid, trying PER decode
H225RAS: ASN.1 decode error: More data needed for complete decode (consumed: 0 bytes)
dissector_h225ras
H225RAS: Attempting to decode 24 bytes of data
H225RAS: First few bytes: 2b 00 5f 59
H225RAS: BER decode failed or invalid, trying PER decode
H225RAS: Successfully decoded RAS message
H225RAS: RAS message present value: 11
H225RAS: Decoded message type: admissionConfirm
{proto: "h225ras", teid: "", outer_sip: "", outer_dip: "", msisdn: "", imei: "", imsi: "", tac: "", plmn_id: "", uli: "", base_type: "", fixed_account: "", esn: "", apn: "", plmn_id: "", ecgi_mnc: "", lac: "", ci: "", sac: "", uli: "", ecgi: "", bsid: "", direction: "", ruleid: "", label: "", bs: "", bflag: "", operator: "", rat: "", teid: "", msisdn: "", imei: "", imsi: "", tac: "", PacketSN: "2", SessionSN: "2", RecordSN: "1", CaptureTime: "**********", CarriedTime: "", DissectTime: "**********", PacketLen: "66", EffectiveLen: "66", LinkType: "", ProtoLayers: "eth:ipv4:udp", ProtoFlags: "", C2S: "S2C", SrcIp: "***********", DstIp: "***********", SrcPort: "1719", DstPort: "58535", protocol: "h225ras", message_type: "admissionConfirm", data_length: "24", decode_status: "success", decode_error: "", request_seq_num: "24410", endpoint_identifier: "", gatekeeper_identifier: "", call_reference_value: "", bandwidth: "**********", reject_reason: "", disengage_reason: "", validation_error: "", asn1_decode_consumed: "190", }
Q.931 dissector called with 232 bytes
Q931: Found User-User IE at offset 39, length 193
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=196
H225: First few bytes: 7e 00 c1 05
H225: Found User-User IE, length=193
H225: H.225 protocol discriminator found, h225_len=192
H225: Attempting PER decode of 192 bytes
H225: H.225 data first few bytes: 20 a8 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
Q.931 dissector called with 105 bytes
Q931: Found User-User IE at offset 19, length 86
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=89
H225: First few bytes: 7e 00 56 05
H225: Found User-User IE, length=86
H225: H.225 protocol discriminator found, h225_len=85
H225: Attempting PER decode of 85 bytes
H225: H.225 data first few bytes: 21 80 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
Q.931 dissector called with 105 bytes
Q931: Found User-User IE at offset 19, length 86
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=89
H225: First few bytes: 7e 00 56 05
H225: Found User-User IE, length=86
H225: H.225 protocol discriminator found, h225_len=85
H225: Attempting PER decode of 85 bytes
H225: H.225 data first few bytes: 23 80 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
Q.931 dissector called with 156 bytes
Q931: Found User-User IE at offset 10, length 146
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=149
H225: First few bytes: 7e 00 92 05
H225: Found User-User IE, length=146
H225: H.225 protocol discriminator found, h225_len=145
H225: Attempting PER decode of 145 bytes
H225: H.225 data first few bytes: 26 90 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
Q.931 dissector called with 132 bytes
Q931: Found User-User IE at offset 8, length 124
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=127
H225: First few bytes: 7e 00 7c 05
H225: Found User-User IE, length=124
H225: H.225 protocol discriminator found, h225_len=123
H225: Attempting PER decode of 123 bytes
H225: H.225 data first few bytes: 28 10 01 00
H225: PER decode successful
H225: Successfully decoded H323_UserInformation
H225: Processing H323_UU_PDU
H225: H.245 tunnelling field not present
H225: No H.245 control data present
H225: H.323 message body present value: 9
H225: Message type: empty
{proto: "h225", teid: "", outer_sip: "", outer_dip: "", msisdn: "", imei: "", imsi: "", tac: "", plmn_id: "", uli: "", base_type: "", fixed_account: "", esn: "", apn: "", plmn_id: "", ecgi_mnc: "", lac: "", ci: "", sac: "", uli: "", ecgi: "", bsid: "", direction: "", ruleid: "", label: "", bs: "", bflag: "", operator: "", rat: "", teid: "", msisdn: "", imei: "", imsi: "", tac: "", PacketSN: "7", SessionSN: "3", RecordSN: "6", CaptureTime: "**********", CarriedTime: "", DissectTime: "**********", PacketLen: "190", EffectiveLen: "190", LinkType: "", ProtoLayers: "eth:ipv4:tcp:tpkt:q931", ProtoFlags: "", C2S: "S2C", SrcIp: "************", DstIp: "***********", SrcPort: "1720", DstPort: "18742", protocol: "h225", version: "", message_type: "empty", protocol_type: "CS", data_length: "127", decode_status: "success", decode_error: "", h323_uu_pdu_present: "1", user_data_present: "0", h323_message_body_type: "empty", h245_tunnelling: "", h245_control_present: "", h245_control_count: "", nonstandard_data_present: "", protocol_identifier: "", call_identifier: "", conference_id: "", source_info_present: "", dest_info_present: "", dest_call_signalling_address: "", source_address: "", dest_address: "", dest_extra_calling: "", dest_extra_call_info: "", facility_reason: "", release_complete_reason: "", h245_control_0_size: "", h245_control_0_sample: "", h245_control_1_size: "", h245_control_1_sample: "", h245_control_2_size: "", h245_control_2_sample: "", active_mc: "", media_wait_for_connect: "", can_overlap_send: "", request_seq_num: "", endpoint_identifier: "", gatekeeper_identifier: "", call_reference_value: "", bandwidth: "", reject_reason: "", disengage_reason: "", validation_error: "", asn1_decode_consumed: "36", user_user_ie_offset: "", user_user_ie_length: "123", parse_warnings: "", }
Q.931 dissector called with 80 bytes
Q931: Found User-User IE at offset 10, length 70
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=73
H225: First few bytes: 7e 00 46 05
H225: Found User-User IE, length=70
H225: H.225 protocol discriminator found, h225_len=69
H225: Attempting PER decode of 69 bytes
H225: H.225 data first few bytes: 26 90 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
Q.931 dissector called with 23 bytes
Q931: Found User-User IE at offset 8, length 15
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=18
H225: First few bytes: 7e 00 0f 05
H225: Found User-User IE, length=15
H225: H.225 protocol discriminator found, h225_len=14
H225: Attempting PER decode of 14 bytes
H225: H.225 data first few bytes: 28 10 01 00
H225: PER decode successful
H225: Successfully decoded H323_UserInformation
H225: Processing H323_UU_PDU
H225: H.245 tunnelling field not present
H225: No H.245 control data present
H225: H.323 message body present value: 9
H225: Message type: empty
{proto: "h225", teid: "", outer_sip: "", outer_dip: "", msisdn: "", imei: "", imsi: "", tac: "", plmn_id: "", uli: "", base_type: "", fixed_account: "", esn: "", apn: "", plmn_id: "", ecgi_mnc: "", lac: "", ci: "", sac: "", uli: "", ecgi: "", bsid: "", direction: "", ruleid: "", label: "", bs: "", bflag: "", operator: "", rat: "", teid: "", msisdn: "", imei: "", imsi: "", tac: "", PacketSN: "9", SessionSN: "3", RecordSN: "8", CaptureTime: "**********", CarriedTime: "", DissectTime: "**********", PacketLen: "81", EffectiveLen: "81", LinkType: "", ProtoLayers: "eth:ipv4:tcp:tpkt:q931", ProtoFlags: "", C2S: "S2C", SrcIp: "************", DstIp: "***********", SrcPort: "1720", DstPort: "18742", protocol: "h225", version: "", message_type: "empty", protocol_type: "CS", data_length: "18", decode_status: "success", decode_error: "", h323_uu_pdu_present: "1", user_data_present: "0", h323_message_body_type: "empty", h245_tunnelling: "", h245_control_present: "", h245_control_count: "", nonstandard_data_present: "", protocol_identifier: "", call_identifier: "", conference_id: "", source_info_present: "", dest_info_present: "", dest_call_signalling_address: "", source_address: "", dest_address: "", dest_extra_calling: "", dest_extra_call_info: "", facility_reason: "", release_complete_reason: "", h245_control_0_size: "", h245_control_0_sample: "", h245_control_1_size: "", h245_control_1_sample: "", h245_control_2_size: "", h245_control_2_sample: "", active_mc: "", media_wait_for_connect: "", can_overlap_send: "", request_seq_num: "", endpoint_identifier: "", gatekeeper_identifier: "", call_reference_value: "", bandwidth: "", reject_reason: "", disengage_reason: "", validation_error: "", asn1_decode_consumed: "36", user_user_ie_offset: "", user_user_ie_length: "14", parse_warnings: "", }
Q.931 dissector called with 77 bytes
Q931: Found User-User IE at offset 10, length 67
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=70
H225: First few bytes: 7e 00 43 05
H225: Found User-User IE, length=67
H225: H.225 protocol discriminator found, h225_len=66
H225: Attempting PER decode of 66 bytes
H225: H.225 data first few bytes: 26 90 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
Q.931 dissector called with 22 bytes
Q931: Found User-User IE at offset 8, length 14
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=17
H225: First few bytes: 7e 00 0e 05
H225: Found User-User IE, length=14
H225: H.225 protocol discriminator found, h225_len=13
H225: Attempting PER decode of 13 bytes
H225: H.225 data first few bytes: 28 10 01 00
H225: PER decode successful
H225: Successfully decoded H323_UserInformation
H225: Processing H323_UU_PDU
H225: H.245 tunnelling field not present
H225: No H.245 control data present
H225: H.323 message body present value: 9
H225: Message type: empty
{proto: "h225", teid: "", outer_sip: "", outer_dip: "", msisdn: "", imei: "", imsi: "", tac: "", plmn_id: "", uli: "", base_type: "", fixed_account: "", esn: "", apn: "", plmn_id: "", ecgi_mnc: "", lac: "", ci: "", sac: "", uli: "", ecgi: "", bsid: "", direction: "", ruleid: "", label: "", bs: "", bflag: "", operator: "", rat: "", teid: "", msisdn: "", imei: "", imsi: "", tac: "", PacketSN: "11", SessionSN: "3", RecordSN: "10", CaptureTime: "**********", CarriedTime: "", DissectTime: "**********", PacketLen: "80", EffectiveLen: "80", LinkType: "", ProtoLayers: "eth:ipv4:tcp:tpkt:q931", ProtoFlags: "", C2S: "S2C", SrcIp: "************", DstIp: "***********", SrcPort: "1720", DstPort: "18742", protocol: "h225", version: "", message_type: "empty", protocol_type: "CS", data_length: "17", decode_status: "success", decode_error: "", h323_uu_pdu_present: "1", user_data_present: "0", h323_message_body_type: "empty", h245_tunnelling: "", h245_control_present: "", h245_control_count: "", nonstandard_data_present: "", protocol_identifier: "", call_identifier: "", conference_id: "", source_info_present: "", dest_info_present: "", dest_call_signalling_address: "", source_address: "", dest_address: "", dest_extra_calling: "", dest_extra_call_info: "", facility_reason: "", release_complete_reason: "", h245_control_0_size: "", h245_control_0_sample: "", h245_control_1_size: "", h245_control_1_sample: "", h245_control_2_size: "", h245_control_2_sample: "", active_mc: "", media_wait_for_connect: "", can_overlap_send: "", request_seq_num: "", endpoint_identifier: "", gatekeeper_identifier: "", call_reference_value: "", bandwidth: "", reject_reason: "", disengage_reason: "", validation_error: "", asn1_decode_consumed: "36", user_user_ie_offset: "", user_user_ie_length: "13", parse_warnings: "", }
Q.931 dissector called with 76 bytes
Q931: Found User-User IE at offset 10, length 66
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=69
H225: First few bytes: 7e 00 42 05
H225: Found User-User IE, length=66
H225: H.225 protocol discriminator found, h225_len=65
H225: Attempting PER decode of 65 bytes
H225: H.225 data first few bytes: 26 90 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
Q.931 dissector called with 149 bytes
Q931: Found User-User IE at offset 24, length 125
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=128
H225: First few bytes: 7e 00 7d 05
H225: Found User-User IE, length=125
H225: H.225 protocol discriminator found, h225_len=124
H225: Attempting PER decode of 124 bytes
H225: H.225 data first few bytes: 22 80 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
Q.931 dissector called with 150 bytes
Q931: Found User-User IE at offset 10, length 140
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=143
H225: First few bytes: 7e 00 8c 05
H225: Found User-User IE, length=140
H225: H.225 protocol discriminator found, h225_len=139
H225: Attempting PER decode of 139 bytes
H225: H.225 data first few bytes: 26 90 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
Q.931 dissector called with 23 bytes
Q931: Found User-User IE at offset 8, length 15
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=18
H225: First few bytes: 7e 00 0f 05
H225: Found User-User IE, length=15
H225: H.225 protocol discriminator found, h225_len=14
H225: Attempting PER decode of 14 bytes
H225: H.225 data first few bytes: 28 10 01 00
H225: PER decode successful
H225: Successfully decoded H323_UserInformation
H225: Processing H323_UU_PDU
H225: H.245 tunnelling field not present
H225: No H.245 control data present
H225: H.323 message body present value: 9
H225: Message type: empty
{proto: "h225", teid: "", outer_sip: "", outer_dip: "", msisdn: "", imei: "", imsi: "", tac: "", plmn_id: "", uli: "", base_type: "", fixed_account: "", esn: "", apn: "", plmn_id: "", ecgi_mnc: "", lac: "", ci: "", sac: "", uli: "", ecgi: "", bsid: "", direction: "", ruleid: "", label: "", bs: "", bflag: "", operator: "", rat: "", teid: "", msisdn: "", imei: "", imsi: "", tac: "", PacketSN: "19", SessionSN: "5", RecordSN: "14", CaptureTime: "**********", CarriedTime: "", DissectTime: "**********", PacketLen: "81", EffectiveLen: "81", LinkType: "", ProtoLayers: "eth:ipv4:tcp:tpkt:q931", ProtoFlags: "", C2S: "S2C", SrcIp: "************", DstIp: "***********", SrcPort: "1720", DstPort: "18742", protocol: "h225", version: "", message_type: "empty", protocol_type: "CS", data_length: "18", decode_status: "success", decode_error: "", h323_uu_pdu_present: "1", user_data_present: "0", h323_message_body_type: "empty", h245_tunnelling: "", h245_control_present: "", h245_control_count: "", nonstandard_data_present: "", protocol_identifier: "", call_identifier: "", conference_id: "", source_info_present: "", dest_info_present: "", dest_call_signalling_address: "", source_address: "", dest_address: "", dest_extra_calling: "", dest_extra_call_info: "", facility_reason: "", release_complete_reason: "", h245_control_0_size: "", h245_control_0_sample: "", h245_control_1_size: "", h245_control_1_sample: "", h245_control_2_size: "", h245_control_2_sample: "", active_mc: "", media_wait_for_connect: "", can_overlap_send: "", request_seq_num: "", endpoint_identifier: "", gatekeeper_identifier: "", call_reference_value: "", bandwidth: "", reject_reason: "", disengage_reason: "", validation_error: "", asn1_decode_consumed: "36", user_user_ie_offset: "", user_user_ie_length: "14", parse_warnings: "", }
Q.931 dissector called with 45 bytes
Q931: Found User-User IE at offset 12, length 33
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=36
H225: First few bytes: 7e 00 21 05
H225: Found User-User IE, length=33
H225: H.225 protocol discriminator found, h225_len=32
H225: Attempting PER decode of 32 bytes
H225: H.225 data first few bytes: 25 80 06 00
H225: PER decode failed, trying BER decode
H225: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
H225: Failed to parse CS message
dissector_h225ras
H225RAS: Attempting to decode 91 bytes of data
H225RAS: First few bytes: 3e 5f 5a 1e
H225RAS: BER decode failed or invalid, trying PER decode
H225RAS: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
dissector_h225ras
H225RAS: Data too short: 3 bytes
processed packets: 22
output precords: 5
