#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <arpa/inet.h>

// Include ASN.1 generated headers (will be generated at build time)
#include "asn1_discovery.h"

#define PROTO_NAME     "h235"

// Helper function to extract message type from H235SecurityMessage
static const char* get_h235_message_type(const H235SecurityMessage_t *message)
{
    if (!message) {
        return "unknown";
    }

    switch (message->present) {
        case H235SecurityMessage_PR_authenticationRequest:
            return "authenticationRequest";
        case H235SecurityMessage_PR_authenticationConfirm:
            return "authenticationConfirm";
        case H235SecurityMessage_PR_authenticationReject:
            return "authenticationReject";
        case H235SecurityMessage_PR_keyExchangeRequest:
            return "keyExchangeRequest";
        case H235SecurityMessage_PR_keyExchangeResponse:
            return "keyExchangeResponse";
        case H235SecurityMessage_PR_securityCapabilities:
            return "securityCapabilities";
        case H235SecurityMessage_PR_encryptionSync:
            return "encryptionSync";
        default:
            return "unknown";
    }
}

// Helper function to extract authentication mode
static const char* get_authentication_mode(const AuthenticationMode_t *mode)
{
    if (!mode) {
        return "unknown";
    }

    switch (mode->present) {
        case AuthenticationMode_PR_nonStandard:
            return "nonStandard";
        case AuthenticationMode_PR_pwdHash:
            return "pwdHash";
        case AuthenticationMode_PR_pwdSymEnc:
            return "pwdSymEnc";
        case AuthenticationMode_PR_certSign:
            return "certSign";
        case AuthenticationMode_PR_ipsec:
            return "ipsec";
        case AuthenticationMode_PR_tls:
            return "tls";
        case AuthenticationMode_PR_nonRepudiation:
            return "nonRepudiation";
        default:
            return "unknown";
    }
}

// Helper function to extract authentication reject reason
static const char* get_auth_reject_reason(const AuthenticationRejectReason_t *reason)
{
    if (!reason) {
        return "unknown";
    }

    switch (reason->present) {
        case AuthenticationRejectReason_PR_neededFeatureNotSupported:
            return "neededFeatureNotSupported";
        case AuthenticationRejectReason_PR_genericDataReason:
            return "genericDataReason";
        case AuthenticationRejectReason_PR_invalidCertificate:
            return "invalidCertificate";
        case AuthenticationRejectReason_PR_invalidTime:
            return "invalidTime";
        case AuthenticationRejectReason_PR_invalidLoginCredentials:
            return "invalidLoginCredentials";
        case AuthenticationRejectReason_PR_duplicateMediaKey:
            return "duplicateMediaKey";
        case AuthenticationRejectReason_PR_securityWrongSyncTime:
            return "securityWrongSyncTime";
        case AuthenticationRejectReason_PR_securityReplay:
            return "securityReplay";
        case AuthenticationRejectReason_PR_securityWrongGeneralID:
            return "securityWrongGeneralID";
        case AuthenticationRejectReason_PR_securityWrongSendersID:
            return "securityWrongSendersID";
        case AuthenticationRejectReason_PR_securityIntegrityFailed:
            return "securityIntegrityFailed";
        case AuthenticationRejectReason_PR_securityWrongOID:
            return "securityWrongOID";
        case AuthenticationRejectReason_PR_securityDHmismatch:
            return "securityDHmismatch";
        case AuthenticationRejectReason_PR_securityCertificateExpired:
            return "securityCertificateExpired";
        case AuthenticationRejectReason_PR_securityCertificateDateInvalid:
            return "securityCertificateDateInvalid";
        case AuthenticationRejectReason_PR_securityCertificateRevoked:
            return "securityCertificateRevoked";
        case AuthenticationRejectReason_PR_securityCertificateNotReadable:
            return "securityCertificateNotReadable";
        case AuthenticationRejectReason_PR_securityCertificateSignatureInvalid:
            return "securityCertificateSignatureInvalid";
        case AuthenticationRejectReason_PR_securityCertificateMissing:
            return "securityCertificateMissing";
        case AuthenticationRejectReason_PR_securityCertificateIncomplete:
            return "securityCertificateIncomplete";
        case AuthenticationRejectReason_PR_securityUnsupportedCertificateAlgOID:
            return "securityUnsupportedCertificateAlgOID";
        case AuthenticationRejectReason_PR_securityUnknownCA:
            return "securityUnknownCA";
        default:
            return "unknown";
    }
}

// Helper function to extract authentication header information
static void extract_auth_header(precord_t *precord, const AuthenticationHeader_t *header)
{
    if (!header) {
        return;
    }

    // Extract authentication mode
    const char *auth_mode = get_authentication_mode(&header->authenticationMode);
    precord_put(precord, "auth_mode", string, auth_mode);

    // Extract timestamp if present
    if (header->timeStamp) {
        precord_put(precord, "timestamp", uinteger, (uint32_t)*header->timeStamp);
    }

    // Extract challenge if present
    if (header->challenge && header->challenge->buf) {
        precord_put(precord, "challenge", bytes, header->challenge->buf, header->challenge->size);
    }

    // Extract random value if present
    if (header->random) {
        precord_put(precord, "random", uinteger, (uint32_t)*header->random);
    }

    // Extract algorithm OID if present
    if (header->algorithmOID) {
        // Convert OID to string representation (simplified)
        precord_put(precord, "algorithm_oid", bytes, header->algorithmOID->buf, header->algorithmOID->size);
    }
}

// Helper function to extract certificate information
static void extract_certificates(precord_t *precord, const struct AuthenticationRequest__certificate *certs)
{
    if (!certs) {
        return;
    }

    precord_put(precord, "cert_count", uinteger, (uint32_t)certs->list.count);

    // Only extract first certificate for simplicity
    if (certs->list.count > 0 && certs->list.array[0]) {
        Certificate_t *cert = certs->list.array[0];

        switch (cert->present) {
            case Certificate_PR_x509v3:
                precord_put(precord, "cert_type", string, "x509v3");
                if (cert->choice.x509v3.buf) {
                    precord_put(precord, "cert_data", bytes, cert->choice.x509v3.buf, cert->choice.x509v3.size);
                }
                break;
            case Certificate_PR_pgp:
                precord_put(precord, "cert_type", string, "pgp");
                if (cert->choice.pgp.buf) {
                    precord_put(precord, "cert_data", bytes, cert->choice.pgp.buf, cert->choice.pgp.size);
                }
                break;
            default:
                precord_put(precord, "cert_type", string, "unknown");
                break;
        }
    }
}

// Helper function to extract clear tokens (simplified)
static void extract_clear_tokens(precord_t *precord, const struct AuthenticationRequest__tokens *tokens)
{
    if (!tokens) {
        return;
    }

    precord_put(precord, "token_count", uinteger, (uint32_t)tokens->list.count);

    // Only extract first token for simplicity
    if (tokens->list.count > 0 && tokens->list.array[0]) {
        ClearToken_t *token = tokens->list.array[0];

        // Extract token OID
        if (token->tokenOID.buf) {
            precord_put(precord, "token_oid", bytes, token->tokenOID.buf, token->tokenOID.size);
        }

        // Extract timestamp if present
        if (token->timeStamp) {
            precord_put(precord, "token_timestamp", uinteger, (uint32_t)*token->timeStamp);
        }

        // Extract challenge if present
        if (token->challenge && token->challenge->buf) {
            precord_put(precord, "token_challenge", bytes, token->challenge->buf, token->challenge->size);
        }

        // Extract random if present
        if (token->random) {
            precord_put(precord, "token_random", uinteger, (uint32_t)*token->random);
        }

        // Extract general ID if present
        if (token->generalID && token->generalID->buf) {
            precord_put(precord, "token_general_id", string, (char*)token->generalID->buf);
        }
    }
}

// Main dissector function
static int h235_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Get raw data from mbuf
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // Decode H.235 security message using asn1c
    H235SecurityMessage_t *message = NULL;
    asn_dec_rval_t decode_result;

    decode_result = ber_decode(0, &asn_DEF_H235SecurityMessage, (void **)&message, data, data_len);
    
    if (decode_result.code != RC_OK) {
        // Decoding failed
        precord_put(precord, "decode_error", string, "H.235 decode failed");
        return 0; // Return 0 to indicate no bytes consumed
    }

    // Extract basic message information
    const char *msg_type = get_h235_message_type(message);
    precord_put(precord, "message_type", string, msg_type);

    // Extract specific fields based on message type
    switch (message->present) {
        case H235SecurityMessage_PR_authenticationRequest:
            {
                AuthenticationRequest_t *authReq = &message->choice.authenticationRequest;
                
                // Extract authentication header
                extract_auth_header(precord, &authReq->authenticationHeader);
                
                // Extract challenge if present
                if (authReq->challenge && authReq->challenge->buf) {
                    precord_put(precord, "request_challenge", bytes, authReq->challenge->buf, authReq->challenge->size);
                }
                
                // Extract random if present
                if (authReq->random) {
                    precord_put(precord, "request_random", uinteger, (uint32_t)*authReq->random);
                }
                
                // Extract certificates
                if (authReq->certificate) {
                    extract_certificates(precord, authReq->certificate);
                }
                
                // Extract clear tokens
                if (authReq->tokens) {
                    extract_clear_tokens(precord, authReq->tokens);
                }
                
                // Extract algorithm OID if present
                if (authReq->algorithmOID) {
                    precord_put(precord, "request_algorithm_oid", bytes, authReq->algorithmOID->buf, authReq->algorithmOID->size);
                }
            }
            break;
            
        case H235SecurityMessage_PR_authenticationConfirm:
            {
                AuthenticationConfirm_t *authConf = &message->choice.authenticationConfirm;
                
                // Extract authentication header
                extract_auth_header(precord, &authConf->authenticationHeader);
                
                // Extract response if present
                if (authConf->response && authConf->response->buf) {
                    precord_put(precord, "response", bytes, authConf->response->buf, authConf->response->size);
                }
                
                // Extract random if present
                if (authConf->random) {
                    precord_put(precord, "confirm_random", uinteger, (uint32_t)*authConf->random);
                }
                
                // Extract certificates (simplified for AuthenticationConfirm)
                if (authConf->certificate) {
                    precord_put(precord, "has_certificate", uinteger, 1);
                }

                // Extract clear tokens (simplified for AuthenticationConfirm)
                if (authConf->tokens) {
                    precord_put(precord, "has_tokens", uinteger, 1);
                }
            }
            break;
            
        case H235SecurityMessage_PR_authenticationReject:
            {
                AuthenticationReject_t *authRej = &message->choice.authenticationReject;
                
                // Extract authentication header
                extract_auth_header(precord, &authRej->authenticationHeader);
                
                // Extract reject reason
                const char *reject_reason = get_auth_reject_reason(&authRej->rejectReason);
                precord_put(precord, "reject_reason", string, reject_reason);
            }
            break;
            
        case H235SecurityMessage_PR_keyExchangeRequest:
            {
                KeyExchangeRequest_t *keyReq = &message->choice.keyExchangeRequest;
                
                // Extract request ID
                precord_put(precord, "request_id", uinteger, (uint32_t)keyReq->requestId);
                
                // Extract general ID if present
                if (keyReq->generalID && keyReq->generalID->buf) {
                    precord_put(precord, "general_id", string, (char*)keyReq->generalID->buf);
                }
                
                // Extract senders ID if present
                if (keyReq->sendersID && keyReq->sendersID->buf) {
                    precord_put(precord, "senders_id", string, (char*)keyReq->sendersID->buf);
                }
            }
            break;
            
        case H235SecurityMessage_PR_keyExchangeResponse:
            {
                KeyExchangeResponse_t *keyResp = &message->choice.keyExchangeResponse;
                
                // Extract request ID
                precord_put(precord, "request_id", uinteger, (uint32_t)keyResp->requestId);
                
                // Extract general ID if present
                if (keyResp->generalID && keyResp->generalID->buf) {
                    precord_put(precord, "general_id", string, (char*)keyResp->generalID->buf);
                }
                
                // Extract senders ID if present
                if (keyResp->sendersID && keyResp->sendersID->buf) {
                    precord_put(precord, "senders_id", string, (char*)keyResp->sendersID->buf);
                }
            }
            break;
            
        case H235SecurityMessage_PR_encryptionSync:
            {
                EncryptionSync_t *encSync = &message->choice.encryptionSync;
                
                // Extract sync flag
                precord_put(precord, "sync_flag", uinteger, (uint32_t)encSync->synchFlag);
            }
            break;
            
        default:
            // For other message types, basic information is already extracted
            break;
    }

    // Post the event
    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // Free the decoded message
    ASN_STRUCT_FREE(asn_DEF_H235SecurityMessage, message);
    
    // Return the number of bytes consumed (entire message)
    return (int)data_len;
}

static int h235_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "H.235 Security");
    
    // Basic message fields
    pschema_register_field(pschema, "message_type", YA_FT_STRING, "H.235 message type");
    pschema_register_field(pschema, "decode_error", YA_FT_STRING, "Decode error message");
    
    // Authentication fields
    pschema_register_field(pschema, "auth_mode", YA_FT_STRING, "Authentication mode");
    pschema_register_field(pschema, "timestamp", YA_FT_UINT32, "Timestamp");
    pschema_register_field(pschema, "challenge", YA_FT_BYTES, "Challenge string");
    pschema_register_field(pschema, "random", YA_FT_UINT32, "Random value");
    pschema_register_field(pschema, "algorithm_oid", YA_FT_BYTES, "Algorithm OID");
    
    // Request specific fields
    pschema_register_field(pschema, "request_challenge", YA_FT_BYTES, "Request challenge");
    pschema_register_field(pschema, "request_random", YA_FT_UINT32, "Request random value");
    pschema_register_field(pschema, "request_algorithm_oid", YA_FT_BYTES, "Request algorithm OID");
    
    // Response specific fields
    pschema_register_field(pschema, "response", YA_FT_BYTES, "Authentication response");
    pschema_register_field(pschema, "confirm_random", YA_FT_UINT32, "Confirm random value");
    
    // Reject specific fields
    pschema_register_field(pschema, "reject_reason", YA_FT_STRING, "Authentication reject reason");
    
    // Key exchange fields
    pschema_register_field(pschema, "request_id", YA_FT_UINT32, "Key exchange request ID");
    pschema_register_field(pschema, "general_id", YA_FT_STRING, "General identifier");
    pschema_register_field(pschema, "senders_id", YA_FT_STRING, "Senders identifier");
    
    // Encryption sync fields
    pschema_register_field(pschema, "sync_flag", YA_FT_UINT32, "Encryption sync flag");
    
    // Certificate fields
    pschema_register_field(pschema, "cert_count", YA_FT_UINT32, "Number of certificates");
    pschema_register_field(pschema, "cert_type", YA_FT_STRING, "Certificate type");
    pschema_register_field(pschema, "cert_data", YA_FT_BYTES, "Certificate data");
    pschema_register_field(pschema, "has_certificate", YA_FT_UINT32, "Has certificate");

    // Token fields
    pschema_register_field(pschema, "token_count", YA_FT_UINT32, "Number of tokens");
    pschema_register_field(pschema, "token_oid", YA_FT_BYTES, "Token OID");
    pschema_register_field(pschema, "token_timestamp", YA_FT_UINT32, "Token timestamp");
    pschema_register_field(pschema, "token_challenge", YA_FT_BYTES, "Token challenge");
    pschema_register_field(pschema, "token_random", YA_FT_UINT32, "Token random");
    pschema_register_field(pschema, "token_general_id", YA_FT_STRING, "Token general ID");
    pschema_register_field(pschema, "has_tokens", YA_FT_UINT32, "Has tokens");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "h235",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = h235_schema_reg,
    .dissectFun   = h235_dissect,
    .mountAt      = {
        // H.235 security messages are typically embedded in other H.323 protocols
        // This could be mounted on specific ports or as a sub-dissector
        NXT_MNT_NUMBER("tcp", 1719),  // H.323 RAS port (example)
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(h235)
{
    nxt_dissector_register(&gDissectorDef);
}
