#include <stdio.h>
#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>

#include "asn1_discovery.h"

#define PROTO_NAME     "h245"
#define HEADER_LEN_H245 12

// Helper function to get H.245 message type name
static const char* get_h245_message_type(const MultimediaSystemControlMessage_t *message)
{
    if (!message) {
        return "unknown";
    }

    switch (message->present) {
        case MultimediaSystemControlMessage_PR_request:
            return "request";
        case MultimediaSystemControlMessage_PR_response:
            return "response";
        case MultimediaSystemControlMessage_PR_command:
            return "command";
        case MultimediaSystemControlMessage_PR_indication:
            return "indication";
        default:
            return "unknown";
    }
}

// Helper function to extract audio capability information
#if 0
static void extract_audio_capability(precord_t *precord, const AudioCapability_t *audioCap)
{
    if (!audioCap) {
        return;
    }

    switch (audioCap->present) {
        case AudioCapability_PR_g711Alaw64k:
            precord_put(precord, "audio_codec", string, "g711Alaw64k");
            precord_put(precord, "audio_frames", uinteger, (uint32_t)audioCap->choice.g711Alaw64k);
            break;
        case AudioCapability_PR_g711Ulaw64k:
            precord_put(precord, "audio_codec", string, "g711Ulaw64k");
            precord_put(precord, "audio_frames", uinteger, (uint32_t)audioCap->choice.g711Ulaw64k);
            break;
        case AudioCapability_PR_g722_64k:
            precord_put(precord, "audio_codec", string, "g722_64k");
            precord_put(precord, "audio_frames", uinteger, (uint32_t)audioCap->choice.g722_64k);
            break;
        case AudioCapability_PR_g7231:
            precord_put(precord, "audio_codec", string, "g7231");
            if (audioCap->choice.g7231.maxAl_sduAudioFrames) {
                precord_put(precord, "audio_frames", uinteger, (uint32_t)audioCap->choice.g7231.maxAl_sduAudioFrames);
            }
            precord_put(precord, "silence_suppression", uinteger, audioCap->choice.g7231.silenceSuppression ? 1 : 0);
            break;
        case AudioCapability_PR_g728:
            precord_put(precord, "audio_codec", string, "g728");
            precord_put(precord, "audio_frames", uinteger, (uint32_t)audioCap->choice.g728);
            break;
        case AudioCapability_PR_g729:
            precord_put(precord, "audio_codec", string, "g729");
            precord_put(precord, "audio_frames", uinteger, (uint32_t)audioCap->choice.g729);
            break;
        default:
            precord_put(precord, "audio_codec", string, "unknown");
            break;
    }

    // 添加 h245.audioData 字段标识
    precord_put(precord, "audioData", uinteger, 1);
}
#endif
static
int h245_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    printf("dissect h245\n");
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Get raw data from mbuf
    const uint8_t *data = nxt_mbuf_get_raw(mbuf, 0);
    size_t data_len = nxt_mbuf_get_length(mbuf);

    // Decode H.245 message using asn1c
    MultimediaSystemControlMessage_t *message = NULL;
    asn_dec_rval_t decode_result = ber_decode(0, &asn_DEF_MultimediaSystemControlMessage, (void **)&message, data, data_len);

    if (decode_result.code != RC_OK) {
        precord_put(precord, "decode_error", string, "H.245 decode failed");
        return 0;
    }

    // Extract basic message information
    const char *msg_type = get_h245_message_type(message);
    precord_put(precord, "message_type", string, msg_type);

    // Extract specific fields based on message type
    switch (message->present) {
        case MultimediaSystemControlMessage_PR_request:
            {
                // RequestMessage_t *request = &message->choice.request;
                precord_put(precord, "request_type", string, "request");
                // TODO: Extract more specific request information
            }
            break;
        case MultimediaSystemControlMessage_PR_response:
            {
                // ResponseMessage_t *response = &message->choice.response;
                precord_put(precord, "response_type", string, "response");
                // TODO: Extract more specific response information
            }
            break;
        case MultimediaSystemControlMessage_PR_command:
            {
                // CommandMessage_t *command = &message->choice.command;
                precord_put(precord, "command_type", string, "command");
                // TODO: Extract more specific command information
            }
            break;
        case MultimediaSystemControlMessage_PR_indication:
            {
                // IndicationMessage_t *indication = &message->choice.indication;
                precord_put(precord, "indication_type", string, "indication");
                // TODO: Extract more specific indication information
            }
            break;
        default:
            break;
    }

    // 添加 h245.audioData 字段的通用标识
    precord_put(precord, "audioData", uinteger, 1);

    // Check for nested TPKT structures in remaining data
    size_t consumed_bytes = decode_result.consumed;
    size_t remaining_bytes = data_len - consumed_bytes;

    precord_put(precord, "remaining_data_length", uinteger, (uint32_t)remaining_bytes);

    if (remaining_bytes >= 4) {  // Minimum TPKT header size
        const uint8_t *remaining_data = data + consumed_bytes;
        int nested_count = 0;
        size_t offset = 0;

        // Look for TPKT headers in remaining data
        while (offset + 4 <= remaining_bytes) {
            // Check for TPKT header: version=3, reserved=0
            if (remaining_data[offset] == 0x03 && remaining_data[offset + 1] == 0x00) {
                uint16_t tpkt_length = (remaining_data[offset + 2] << 8) | remaining_data[offset + 3];

                // Validate TPKT length
                if (tpkt_length >= 4 && tpkt_length <= remaining_bytes - offset) {
                    nested_count++;

                    if (nested_count == 1) {
                        // Record first nested TPKT
                        precord_put(precord, "nested_tpkt_detected", uinteger, 1);
                        precord_put(precord, "nested_tpkt_offset", uinteger, (uint32_t)(consumed_bytes + offset));
                        precord_put(precord, "nested_tpkt_length", uinteger, (uint32_t)tpkt_length);

                        // Set handoff for nested TPKT processing
                        nxt_handoff_set_key_of_number(engine, 3); // 3 = nested tpkt
                    }

                    // Move to next potential TPKT
                    offset += tpkt_length;
                } else {
                    // Invalid TPKT length, move to next byte
                    offset++;
                }
            } else {
                // Not a TPKT header, move to next byte
                offset++;
            }
        }

        precord_put(precord, "nested_tpkt_count", uinteger, (uint32_t)nested_count);
    } else {
        precord_put(precord, "nested_tpkt_detected", uinteger, 0);
        precord_put(precord, "nested_tpkt_count", uinteger, 0);
    }

    // Post the event
    nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);

    // Free the decoded message
    ASN_STRUCT_FREE(asn_DEF_MultimediaSystemControlMessage, message);

    // Return the number of bytes consumed (H.245 message only, let engine handle remaining data)
    return (int)consumed_bytes;
}

static
int h245_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
     /* 注册 schema */
     pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "H.245 Control");

     // Basic message fields
     pschema_register_field(pschema, "message_type", YA_FT_STRING, "H.245 message type");
     pschema_register_field(pschema, "decode_error", YA_FT_STRING, "Decode error message");

     // Message type specific fields
     pschema_register_field(pschema, "request_type", YA_FT_STRING, "Request type");
     pschema_register_field(pschema, "response_type", YA_FT_STRING, "Response type");
     pschema_register_field(pschema, "command_type", YA_FT_STRING, "Command type");
     pschema_register_field(pschema, "indication_type", YA_FT_STRING, "Indication type");

     // Audio capability fields
     pschema_register_field(pschema, "audio_codec", YA_FT_STRING, "Audio codec type");
     pschema_register_field(pschema, "audio_frames", YA_FT_UINT32, "Audio frames");
     pschema_register_field(pschema, "silence_suppression", YA_FT_UINT32, "Silence suppression flag");
     pschema_register_field(pschema, "audioData", YA_FT_UINT32, "Audio data present flag");

     // Nested TPKT detection fields
     pschema_register_field(pschema, "nested_tpkt_detected", YA_FT_UINT32, "Nested TPKT detected");
     pschema_register_field(pschema, "nested_tpkt_count", YA_FT_UINT32, "Number of nested TPKT structures");
     pschema_register_field(pschema, "nested_tpkt_offset", YA_FT_UINT32, "Offset of nested TPKT");
     pschema_register_field(pschema, "nested_tpkt_length", YA_FT_UINT32, "Length of nested TPKT");
     pschema_register_field(pschema, "remaining_data_length", YA_FT_UINT32, "Remaining data after H.245 decode");

     return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "h245",
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = h245_schema_reg,
    .dissectFun   = h245_dissect,
    .mountAt      = {
        // H.245 不直接挂载在端口上，而是通过h225cs handoff或者TPKT handoff
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(h245)
{
    nxt_dissector_register(&gDissectorDef);

    // Register handoff rule for nested TPKT processing
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "h245", 3, "tpkt");
}
