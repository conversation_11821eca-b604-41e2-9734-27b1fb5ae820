-- H323-MESSAGES.asn
--
-- Simplified H.225.0 Call signalling and RAS protocols for ASN.1 parsing
-- Based on ITU-T Recommendation H.225.0
-- Simplified for asn1c compatibility
--

-- Module H323-MESSAGES (H.225.0:12/2009)
H323-MESSAGES {itu-t(0) recommendation(0) h(8) h225-0(2250) version(0)
  7 h323-messages(0)} DEFINITIONS AUTOMATIC TAGS ::=
BEGIN

-- =======================================================================
-- Basic Types (simplified from H235-SECURITY-MESSAGES)
-- =======================================================================
ChallengeString ::= OCTET STRING(SIZE (8..128))
TimeStamp ::= INTEGER(1..4294967295)
RandomVal ::= INTEGER
Password ::= BMPString(SIZE (1..128))
Identifier ::= BMPString(SIZE (1..128))

NonStandardParameter ::= SEQUENCE {
  nonStandardIdentifier  OBJECT IDENTIFIER,
  data                   OCTET STRING
}

ClearToken ::= SEQUENCE {
  tokenOID        OBJECT IDENTIFIER,
  timeStamp       TimeStamp OPTIONAL,
  password        Password OPTIONAL,
  challenge       ChallengeString OPTIONAL,
  random          RandomVal OPTIONAL,
  generalID       Identifier OPTIONAL,
  nonStandard     NonStandardParameter OPTIONAL,
  ...
}

CryptoH323Token ::= CHOICE {
  cryptoEPPwdHash  SEQUENCE {
    alias      AliasAddress,
    timeStamp  TimeStamp,
    token      OCTET STRING
  },
  cryptoGKPwdHash  SEQUENCE {
    gatekeeperIdentifier  BMPString(SIZE(1..128)),
    timeStamp             TimeStamp,
    token                 OCTET STRING
  },
  cryptoEPPwdEncr  SEQUENCE {
    alias      AliasAddress,
    timeStamp  TimeStamp,
    token      OCTET STRING
  },
  cryptoGKPwdEncr  SEQUENCE {
    gatekeeperIdentifier  BMPString(SIZE(1..128)),
    timeStamp             TimeStamp,
    token                 OCTET STRING
  },
  ...
}

-- =======================================================================
-- Basic Types and Supporting Structures
-- =======================================================================
ProtocolIdentifier ::= OBJECT IDENTIFIER

CallIdentifier ::= SEQUENCE {
  guid  OCTET STRING(SIZE(16))
}

ConferenceIdentifier ::= OCTET STRING(SIZE(16))

CallReferenceValue ::= INTEGER(0..65535)

EndpointIdentifier ::= BMPString(SIZE(1..128))

TransportAddress ::= CHOICE {
  ipAddress
    SEQUENCE {ip    OCTET STRING(SIZE (4)),
              port  INTEGER(0..65535)},
  ipSourceRoute
    SEQUENCE {ip       OCTET STRING(SIZE (4)),
              port     INTEGER(0..65535),
              route    SEQUENCE OF OCTET STRING(SIZE (4)),
              routing  CHOICE {strict  NULL,
                               loose   NULL,
                               ...},
              ...},
  ipxAddress
    SEQUENCE {node    OCTET STRING(SIZE (6)),
              netnum  OCTET STRING(SIZE (4)),
              port    OCTET STRING(SIZE (2))},
  ip6Address
    SEQUENCE {ip    OCTET STRING(SIZE (16)),
              port  INTEGER(0..65535),
              ...},
  netBios             OCTET STRING(SIZE (16)),
  nsap                OCTET STRING(SIZE (1..20)),
  nonStandardAddress  NonStandardParameter,
  ...
}

AliasAddress ::= CHOICE {
  dialledDigits  IA5String(SIZE (1..128)),
  h323-ID        BMPString(SIZE (1..256)),
  url-ID         IA5String(SIZE (1..512)),
  transportID    TransportAddress,
  email-ID       IA5String(SIZE (1..512)),
  partyNumber    PartyNumber,
  ...
}

PartyNumber ::= CHOICE {
  e164Number        PublicPartyNumber,
  dataPartyNumber   NumberDigits,
  telexPartyNumber  NumberDigits,
  privateNumber     PrivatePartyNumber,
  nationalStandardPartyNumber NumberDigits,
  ...
}

PublicPartyNumber ::= SEQUENCE {
  publicTypeOfNumber  PublicTypeOfNumber,
  publicNumberDigits  NumberDigits
}

PrivatePartyNumber ::= SEQUENCE {
  privateTypeOfNumber  PrivateTypeOfNumber,
  privateNumberDigits  NumberDigits
}

NumberDigits ::= IA5String(SIZE (1..128))

PublicTypeOfNumber ::= CHOICE {
  unknown                 NULL,
  internationalNumber     NULL,
  nationalNumber          NULL,
  networkSpecificNumber   NULL,
  subscriberNumber        NULL,
  abbreviatedNumber       NULL,
  ...
}

PrivateTypeOfNumber ::= CHOICE {
  unknown         NULL,
  level2RegionalNumber NULL,
  level1RegionalNumber NULL,
  pISNSpecificNumber   NULL,
  localNumber          NULL,
  abbreviatedNumber    NULL,
  ...
}

EndpointType ::= SEQUENCE {
  nonStandardData                 NonStandardParameter OPTIONAL,
  vendor                          VendorIdentifier OPTIONAL,
  gatekeeper                      GatekeeperInfo OPTIONAL,
  gateway                         GatewayInfo OPTIONAL,
  mcu                             McuInfo OPTIONAL,
  terminal                        TerminalInfo OPTIONAL,
  mc                              BOOLEAN,
  undefinedNode                   BOOLEAN,
  ...
}

VendorIdentifier ::= SEQUENCE {
  vendor                  H221NonStandard,
  productId               OCTET STRING(SIZE(1..256)) OPTIONAL,
  versionId               OCTET STRING(SIZE(1..256)) OPTIONAL
}

H221NonStandard ::= SEQUENCE {
  t35CountryCode          INTEGER(0..255),
  t35Extension            INTEGER(0..255),
  manufacturerCode        INTEGER(0..65535)
}

GatekeeperInfo ::= SEQUENCE {
  nonStandardData          NonStandardParameter OPTIONAL,
  ...
}

GatewayInfo ::= SEQUENCE {
  nonStandardData          NonStandardParameter OPTIONAL,
  ...
}

McuInfo ::= SEQUENCE {
  nonStandardData          NonStandardParameter OPTIONAL,
  ...
}

TerminalInfo ::= SEQUENCE {
  nonStandardData          NonStandardParameter OPTIONAL,
  ...
}

-- =======================================================================
-- Top level Messages
-- =======================================================================
H323-UserInformation ::= SEQUENCE {
  h323-uu-pdu  H323-UU-PDU,
  user-data
    SEQUENCE {protocol-discriminator  INTEGER(0..255),
              user-information        OCTET STRING(SIZE (1..131)),
              ...} OPTIONAL,
  ...
}

H323-UU-PDU ::= SEQUENCE {
  h323-message-body
    CHOICE {setup             Setup-UUIE,
            callProceeding    CallProceeding-UUIE,
            connect           Connect-UUIE,
            alerting          Alerting-UUIE,
            information       Information-UUIE,
            releaseComplete   ReleaseComplete-UUIE,
            facility          Facility-UUIE,
            ...,
            progress          Progress-UUIE,
            empty             NULL,
            status            Status-UUIE,
            statusInquiry     StatusInquiry-UUIE,
            setupAcknowledge  SetupAcknowledge-UUIE,
            notify            Notify-UUIE},
  nonStandardData                     NonStandardParameter OPTIONAL,
  ...,
  h4501SupplementaryService           SEQUENCE OF OCTET STRING OPTIONAL,
  h245Tunnelling                      BOOLEAN,
  h245Control                         SEQUENCE OF OCTET STRING OPTIONAL,
  nonStandardControl                  SEQUENCE OF NonStandardParameter OPTIONAL,
  callLinkage                         CallLinkage OPTIONAL,
  tunnelledSignallingMessage
    SEQUENCE {tunnelledProtocolID  TunnelledProtocol,
              messageContent       SEQUENCE OF OCTET STRING,
              tunnellingRequired   NULL OPTIONAL,
              nonStandardData      NonStandardParameter OPTIONAL,
              ...} OPTIONAL,
  provisionalRespToH245Tunnelling     NULL OPTIONAL,
  stimulusControl                     StimulusControl OPTIONAL,
  genericData                         SEQUENCE OF GenericData OPTIONAL
}

StimulusControl ::= SEQUENCE {
  nonStandard  NonStandardParameter OPTIONAL,
  isText       NULL OPTIONAL,
  h248Message  OCTET STRING OPTIONAL,
  ...
}

CallLinkage ::= SEQUENCE {
  globalCallId       GloballyUniqueID OPTIONAL,
  threadId           GloballyUniqueID OPTIONAL,
  ...
}

TunnelledProtocol ::= SEQUENCE {
  id           TunnelledProtocolAlternateIdentifier,
  subIdentifier  IA5String(SIZE(1..64)) OPTIONAL,
  ...
}

TunnelledProtocolAlternateIdentifier ::= CHOICE {
  protocolType       IA5String(SIZE(1..64)),
  protocolVariant    IA5String(SIZE(1..64)),
  ...
}

GenericData ::= SEQUENCE {
  id           GenericIdentifier,
  parameters   SEQUENCE OF EnumeratedParameter OPTIONAL,
  ...
}

GenericIdentifier ::= CHOICE {
  standard       INTEGER(0..16383),
  oid            OBJECT IDENTIFIER,
  nonStandard    GloballyUniqueID,
  ...
}

EnumeratedParameter ::= SEQUENCE {
  id           ParameterIdentifier,
  content      Content OPTIONAL,
  ...
}

ParameterIdentifier ::= CHOICE {
  standard       INTEGER(1..127),
  oid            OBJECT IDENTIFIER,
  nonStandard    GloballyUniqueID,
  ...
}

Content ::= CHOICE {
  raw            OCTET STRING,
  text           IA5String,
  unicode        BMPString,
  bool           BOOLEAN,
  number8        INTEGER(0..255),
  number16       INTEGER(0..65535),
  number32       INTEGER(0..4294967295),
  id             GenericIdentifier,
  alias          AliasAddress,
  transport      TransportAddress,
  compound       SEQUENCE OF EnumeratedParameter,
  nested         SEQUENCE OF GenericData,
  ...
}

GloballyUniqueID ::= OCTET STRING(SIZE(16))

-- Message types need to be defined
Setup-UUIE ::= SEQUENCE {
  protocolIdentifier             ProtocolIdentifier,
  h245Address                    TransportAddress OPTIONAL,
  sourceAddress                  SEQUENCE OF AliasAddress OPTIONAL,
  sourceInfo                     EndpointType,
  destinationAddress             SEQUENCE OF AliasAddress OPTIONAL,
  destCallSignalAddress          TransportAddress OPTIONAL,
  activeMC                       BOOLEAN,
  conferenceID                   ConferenceIdentifier,
  conferenceGoal                 ConferenceGoal,
  callType                       CallType,
  sourceCallSignalAddress        TransportAddress OPTIONAL,
  callIdentifier                 CallIdentifier,
  tokens                         SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens                   SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart                      SEQUENCE OF OCTET STRING OPTIONAL,
  mediaWaitForConnect            BOOLEAN,
  canOverlapSend                 BOOLEAN,
  endpointIdentifier             EndpointIdentifier OPTIONAL,
  multipleCalls                  BOOLEAN,
  maintainConnection             BOOLEAN,
  ...
}

CallProceeding-UUIE ::= SEQUENCE {
  protocolIdentifier     ProtocolIdentifier,
  destinationInfo        EndpointType,
  h245Address            TransportAddress OPTIONAL,
  callIdentifier         CallIdentifier,
  tokens                 SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens           SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart              SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls          BOOLEAN,
  maintainConnection     BOOLEAN,
  ...
}

Connect-UUIE ::= SEQUENCE {
  protocolIdentifier        ProtocolIdentifier,
  h245Address               TransportAddress OPTIONAL,
  destinationInfo           EndpointType,
  conferenceID              ConferenceIdentifier,
  callIdentifier            CallIdentifier,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart                 SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls             BOOLEAN,
  maintainConnection        BOOLEAN,
  ...
}

Alerting-UUIE ::= SEQUENCE {
  protocolIdentifier        ProtocolIdentifier,
  destinationInfo           EndpointType,
  h245Address               TransportAddress OPTIONAL,
  callIdentifier            CallIdentifier,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart                 SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls             BOOLEAN,
  maintainConnection        BOOLEAN,
  ...
}

Information-UUIE ::= SEQUENCE {
  protocolIdentifier     ProtocolIdentifier,
  callIdentifier         CallIdentifier,
  tokens                 SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens           SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart              SEQUENCE OF OCTET STRING OPTIONAL,
  ...
}

ReleaseComplete-UUIE ::= SEQUENCE {
  protocolIdentifier        ProtocolIdentifier,
  reason                    ReleaseCompleteReason OPTIONAL,
  callIdentifier            CallIdentifier,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

Facility-UUIE ::= SEQUENCE {
  protocolIdentifier         ProtocolIdentifier,
  alternativeAddress         TransportAddress OPTIONAL,
  conferenceID               ConferenceIdentifier OPTIONAL,
  reason                     FacilityReason,
  callIdentifier             CallIdentifier,
  tokens                     SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens               SEQUENCE OF CryptoH323Token OPTIONAL,
  h245Address                TransportAddress OPTIONAL,
  fastStart                  SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls              BOOLEAN,
  maintainConnection         BOOLEAN,
  ...
}

Progress-UUIE ::= SEQUENCE {
  protocolIdentifier     ProtocolIdentifier,
  destinationInfo        EndpointType,
  h245Address            TransportAddress OPTIONAL,
  callIdentifier         CallIdentifier,
  tokens                 SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens           SEQUENCE OF CryptoH323Token OPTIONAL,
  fastStart              SEQUENCE OF OCTET STRING OPTIONAL,
  multipleCalls          BOOLEAN,
  maintainConnection     BOOLEAN,
  ...
}

Status-UUIE ::= SEQUENCE {
  protocolIdentifier  ProtocolIdentifier,
  callIdentifier      CallIdentifier,
  tokens              SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens        SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

StatusInquiry-UUIE ::= SEQUENCE {
  protocolIdentifier  ProtocolIdentifier,
  callIdentifier      CallIdentifier,
  tokens              SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens        SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

SetupAcknowledge-UUIE ::= SEQUENCE {
  protocolIdentifier  ProtocolIdentifier,
  callIdentifier      CallIdentifier,
  tokens              SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens        SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

Notify-UUIE ::= SEQUENCE {
  protocolIdentifier        ProtocolIdentifier,
  callIdentifier            CallIdentifier,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

-- Supporting types
ConferenceGoal ::= CHOICE {
  create                               NULL,
  join                                 NULL,
  invite                               NULL,
  ...
}

CallType ::= CHOICE {
  pointToPoint            NULL,
  oneToN                  NULL,
  nToOne                  NULL,
  nToN                    NULL,
  ...
}

ReleaseCompleteReason ::= CHOICE {
  noBandwidth                  NULL,
  gatekeeperResources          NULL,
  unreachableDestination       NULL,
  destinationRejection         NULL,
  invalidRevision              NULL,
  noPermission                 NULL,
  unreachableGatekeeper        NULL,
  gatewayResources             NULL,
  badFormatAddress             NULL,
  adaptiveBusy                 NULL,
  inConf                       NULL,
  undefinedReason              NULL,
  ...
}

FacilityReason ::= CHOICE {
  routeCallToGatekeeper   NULL,
  callForwarded           NULL,
  routeCallToMC           NULL,
  undefinedReason         NULL,
  ...
}

-- =======================================================================
-- H.225.0 RAS Messages
-- =======================================================================
RasMessage ::= CHOICE {
  gatekeeperRequest         [0] GatekeeperRequest,
  gatekeeperConfirm         [1] GatekeeperConfirm,
  gatekeeperReject          [2] GatekeeperReject,
  registrationRequest       [3] RegistrationRequest,
  registrationConfirm       [4] RegistrationConfirm,
  registrationReject        [5] RegistrationReject,
  unregistrationRequest     [6] UnregistrationRequest,
  unregistrationConfirm     [7] UnregistrationConfirm,
  unregistrationReject      [8] UnregistrationReject,
  admissionRequest          [9] AdmissionRequest,
  admissionConfirm          [10] AdmissionConfirm,
  admissionReject           [11] AdmissionReject,
  bandwidthRequest          [12] BandwidthRequest,
  bandwidthConfirm          [13] BandwidthConfirm,
  bandwidthReject           [14] BandwidthReject,
  disengageRequest          [15] DisengageRequest,
  disengageConfirm          [16] DisengageConfirm,
  disengageReject           [17] DisengageReject,
  locationRequest           [18] LocationRequest,
  locationConfirm           [19] LocationConfirm,
  locationReject            [20] LocationReject,
  infoRequest               [21] InfoRequest,
  infoRequestResponse       [22] InfoRequestResponse,
  nonStandardMessage        [23] NonStandardMessage,
  unknownMessageResponse    [24] UnknownMessageResponse,
  ...,
  requestInProgress         [25] RequestInProgress,
  resourcesAvailableIndicate [26] ResourcesAvailableIndicate,
  resourcesAvailableConfirm [27] ResourcesAvailableConfirm,
  infoRequestAck            [28] InfoRequestAck,
  infoRequestNak            [29] InfoRequestNak,
  serviceControlIndication  [30] ServiceControlIndication,
  serviceControlResponse    [31] ServiceControlResponse
}

-- Basic RAS message types
RequestSeqNum ::= INTEGER(1..65535)

GatekeeperRequest ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  protocolIdentifier        ProtocolIdentifier,
  nonStandardData           NonStandardParameter OPTIONAL,
  rasAddress                TransportAddress,
  endpointType              EndpointType,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  callServices              QseriesOptions OPTIONAL,
  endpointAlias             SEQUENCE OF AliasAddress OPTIONAL,
  ...
}

GatekeeperConfirm ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  protocolIdentifier        ProtocolIdentifier,
  nonStandardData           NonStandardParameter OPTIONAL,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  rasAddress                TransportAddress,
  ...
}

GatekeeperReject ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  protocolIdentifier        ProtocolIdentifier,
  nonStandardData           NonStandardParameter OPTIONAL,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  rejectReason              GatekeeperRejectReason,
  ...
}

RegistrationRequest ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  protocolIdentifier        ProtocolIdentifier,
  nonStandardData           NonStandardParameter OPTIONAL,
  discoveryComplete         BOOLEAN,
  callSignalAddress         SEQUENCE OF TransportAddress,
  rasAddress                SEQUENCE OF TransportAddress,
  endpointType              EndpointType,
  endpointIdentifier        EndpointIdentifier OPTIONAL,
  endpointAlias             SEQUENCE OF AliasAddress OPTIONAL,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

RegistrationConfirm ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  protocolIdentifier        ProtocolIdentifier,
  nonStandardData           NonStandardParameter OPTIONAL,
  callSignalAddress         SEQUENCE OF TransportAddress,
  endpointIdentifier        EndpointIdentifier,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

RegistrationReject ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  protocolIdentifier        ProtocolIdentifier,
  nonStandardData           NonStandardParameter OPTIONAL,
  rejectReason              RegistrationRejectReason,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  ...
}

UnregistrationRequest ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  callSignalAddress         SEQUENCE OF TransportAddress,
  endpointAlias             SEQUENCE OF AliasAddress OPTIONAL,
  nonStandardData           NonStandardParameter OPTIONAL,
  endpointIdentifier        EndpointIdentifier OPTIONAL,
  ...
}

UnregistrationConfirm ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

UnregistrationReject ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  rejectReason              UnregRejectReason,
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

AdmissionRequest ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  callType                  CallType,
  callModel                 CallModel OPTIONAL,
  endpointIdentifier        EndpointIdentifier,
  destinationInfo           SEQUENCE OF AliasAddress OPTIONAL,
  destCallSignalAddress     TransportAddress OPTIONAL,
  destExtraCallInfo         SEQUENCE OF AliasAddress OPTIONAL,
  srcInfo                   SEQUENCE OF AliasAddress,
  srcCallSignalAddress      TransportAddress OPTIONAL,
  bandWidth                 BandWidth,
  callReferenceValue        CallReferenceValue,
  nonStandardData           NonStandardParameter OPTIONAL,
  callServices              QseriesOptions OPTIONAL,
  conferenceID              ConferenceIdentifier,
  activeMC                  BOOLEAN,
  answerCall                BOOLEAN,
  canMapAlias               BOOLEAN,
  callIdentifier            CallIdentifier,
  srcAlternatives           SEQUENCE OF Endpoint OPTIONAL,
  destAlternatives          SEQUENCE OF Endpoint OPTIONAL,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

AdmissionConfirm ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  bandWidth                 BandWidth,
  callModel                 CallModel,
  destCallSignalAddress     TransportAddress,
  irrFrequency              INTEGER(1..65535) OPTIONAL,
  nonStandardData           NonStandardParameter OPTIONAL,
  destinationInfo           SEQUENCE OF AliasAddress OPTIONAL,
  destExtraCallInfo         SEQUENCE OF AliasAddress OPTIONAL,
  destinationType           EndpointType OPTIONAL,
  remoteExtensionAddress    SEQUENCE OF AliasAddress OPTIONAL,
  alternateEndpoints        SEQUENCE OF Endpoint OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

AdmissionReject ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  rejectReason              AdmissionRejectReason,
  nonStandardData           NonStandardParameter OPTIONAL,
  altGKInfo                 AltGKInfo OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

BandwidthRequest ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  endpointIdentifier        EndpointIdentifier,
  conferenceID              ConferenceIdentifier,
  callReferenceValue        CallReferenceValue,
  callType                  CallType OPTIONAL,
  bandWidth                 BandWidth,
  nonStandardData           NonStandardParameter OPTIONAL,
  callIdentifier            CallIdentifier,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

BandwidthConfirm ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  bandWidth                 BandWidth,
  nonStandardData           NonStandardParameter OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

BandwidthReject ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  rejectReason              BandwidthRejectReason,
  allowedBandWidth          BandWidth OPTIONAL,
  nonStandardData           NonStandardParameter OPTIONAL,
  altGKInfo                 AltGKInfo OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

DisengageRequest ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  endpointIdentifier        EndpointIdentifier,
  conferenceID              ConferenceIdentifier,
  callReferenceValue        CallReferenceValue,
  disengageReason           DisengageReason,
  nonStandardData           NonStandardParameter OPTIONAL,
  callIdentifier            CallIdentifier,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

DisengageConfirm ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  nonStandardData           NonStandardParameter OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

DisengageReject ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  rejectReason              DisengageRejectReason,
  nonStandardData           NonStandardParameter OPTIONAL,
  altGKInfo                 AltGKInfo OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

LocationRequest ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  endpointIdentifier        EndpointIdentifier OPTIONAL,
  destinationInfo           SEQUENCE OF AliasAddress,
  nonStandardData           NonStandardParameter OPTIONAL,
  replyAddress              TransportAddress,
  sourceInfo                SEQUENCE OF AliasAddress OPTIONAL,
  canMapAlias               BOOLEAN,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

LocationConfirm ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  callSignalAddress         TransportAddress,
  rasAddress                TransportAddress,
  nonStandardData           NonStandardParameter OPTIONAL,
  destinationInfo           SEQUENCE OF AliasAddress OPTIONAL,
  destExtraCallInfo         SEQUENCE OF AliasAddress OPTIONAL,
  destinationType           EndpointType OPTIONAL,
  remoteExtensionAddress    SEQUENCE OF AliasAddress OPTIONAL,
  alternateEndpoints        SEQUENCE OF Endpoint OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

LocationReject ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  rejectReason              LocationRejectReason,
  nonStandardData           NonStandardParameter OPTIONAL,
  altGKInfo                 AltGKInfo OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

InfoRequest ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  callReferenceValue        CallReferenceValue OPTIONAL,
  nonStandardData           NonStandardParameter OPTIONAL,
  replyAddress              TransportAddress OPTIONAL,
  callIdentifier            CallIdentifier,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

InfoRequestResponse ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  requestSeqNum             RequestSeqNum,
  endpointType              EndpointType,
  endpointIdentifier        EndpointIdentifier,
  rasAddress                TransportAddress,
  callSignalAddress         SEQUENCE OF TransportAddress,
  endpointAlias             SEQUENCE OF AliasAddress OPTIONAL,
  perCallInfo               SEQUENCE OF PerCallInfo OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

NonStandardMessage ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  nonStandardData           NonStandardParameter,
  ...
}

UnknownMessageResponse ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  ...
}

RequestInProgress ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  nonStandardData           NonStandardParameter OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  delay                     INTEGER(1..65535),
  ...
}

ResourcesAvailableIndicate ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  protocolIdentifier        ProtocolIdentifier,
  nonStandardData           NonStandardParameter OPTIONAL,
  endpointIdentifier        EndpointIdentifier,
  protocols                 SEQUENCE OF SupportedProtocols,
  almostOutOfResources      BOOLEAN,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

ResourcesAvailableConfirm ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  protocolIdentifier        ProtocolIdentifier,
  nonStandardData           NonStandardParameter OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

InfoRequestAck ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  nonStandardData           NonStandardParameter OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

InfoRequestNak ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  nonStandardData           NonStandardParameter OPTIONAL,
  nakReason                 InfoRequestNakReason,
  altGKInfo                 AltGKInfo OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

ServiceControlIndication ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  nonStandardData           NonStandardParameter OPTIONAL,
  serviceControl            ServiceControlSession,
  endpointIdentifier        EndpointIdentifier OPTIONAL,
  callSpecific              CallSpecificInfo OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

ServiceControlResponse ::= SEQUENCE {
  requestSeqNum             RequestSeqNum,
  result                    ServiceControlResult OPTIONAL,
  nonStandardData           NonStandardParameter OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  ...
}

-- Supporting types for RAS messages
BandWidth ::= INTEGER(0..4294967295)

CallModel ::= CHOICE {
  direct                    NULL,
  gatekeeperRouted          NULL,
  ...
}

QseriesOptions ::= SEQUENCE {
  q932Full                  BOOLEAN,
  q951Full                  BOOLEAN,
  q952Full                  BOOLEAN,
  q953Full                  BOOLEAN,
  q955Full                  BOOLEAN,
  q956Full                  BOOLEAN,
  q957Full                  BOOLEAN,
  q954Info                  Q954Details,
  ...
}

Q954Details ::= SEQUENCE {
  conferenceCalling         BOOLEAN,
  threePartyService         BOOLEAN,
  ...
}

Endpoint ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  aliasAddress              SEQUENCE OF AliasAddress OPTIONAL,
  callSignalAddress         SEQUENCE OF TransportAddress OPTIONAL,
  rasAddress                SEQUENCE OF TransportAddress OPTIONAL,
  endpointType              EndpointType OPTIONAL,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  priority                  INTEGER(0..127) OPTIONAL,
  remoteExtensionAddress    SEQUENCE OF AliasAddress OPTIONAL,
  destExtraCallInfo         SEQUENCE OF AliasAddress OPTIONAL,
  ...
}

AltGKInfo ::= SEQUENCE {
  alternateGatekeeper       SEQUENCE OF AlternateGK,
  altGKisPermanent          BOOLEAN,
  ...
}

AlternateGK ::= SEQUENCE {
  rasAddress                TransportAddress,
  gatekeeperIdentifier      BMPString(SIZE(1..128)) OPTIONAL,
  needToRegister            BOOLEAN,
  priority                  INTEGER(0..127),
  ...
}

PerCallInfo ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  callReferenceValue        CallReferenceValue,
  conferenceID              ConferenceIdentifier,
  originator                BOOLEAN OPTIONAL,
  audio                     SEQUENCE OF RTPSession OPTIONAL,
  video                     SEQUENCE OF RTPSession OPTIONAL,
  data                      SEQUENCE OF TransportChannelInfo OPTIONAL,
  h245                      TransportChannelInfo OPTIONAL,
  callSignaling             TransportChannelInfo OPTIONAL,
  callType                  CallType,
  bandWidth                 BandWidth,
  callModel                 CallModel,
  callIdentifier            CallIdentifier,
  tokens                    SEQUENCE OF ClearToken OPTIONAL,
  cryptoTokens              SEQUENCE OF CryptoH323Token OPTIONAL,
  substituteConfIDs         SEQUENCE OF ConferenceIdentifier,
  pdu                       PerCallInfoPDU OPTIONAL,
  ...
}

RTPSession ::= SEQUENCE {
  rtpAddress                TransportChannelInfo,
  rtcpAddress               TransportChannelInfo,
  cname                     PrintableString,
  ssrc                      INTEGER(1..4294967295),
  sessionId                 INTEGER(1..255),
  associatedSessionIds      SEQUENCE OF INTEGER(1..255),
  ...
}

TransportChannelInfo ::= SEQUENCE {
  sendAddress               TransportAddress OPTIONAL,
  recvAddress               TransportAddress OPTIONAL,
  ...
}

PerCallInfoPDU ::= CHOICE {
  h323pdu                   H323-UU-PDU,
  ...
}

SupportedProtocols ::= CHOICE {
  nonStandardData           NonStandardParameter,
  h310                      H310Caps,
  h320                      H320Caps,
  h321                      H321Caps,
  h322                      H322Caps,
  h323                      H323Caps,
  h324                      H324Caps,
  voice                     VoiceCaps,
  t120-only                 T120OnlyCaps,
  ...
}

H310Caps ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

H320Caps ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

H321Caps ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

H322Caps ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

H323Caps ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

H324Caps ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

VoiceCaps ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

T120OnlyCaps ::= SEQUENCE {
  nonStandardData           NonStandardParameter OPTIONAL,
  ...
}

ServiceControlSession ::= SEQUENCE {
  sessionId                 INTEGER(0..255),
  contents                  CHOICE {
    url                     IA5String(SIZE(1..512)),
    signal                  H248SignalsDescriptor,
    nonStandard             NonStandardParameter,
    ...
  } OPTIONAL,
  reason                    ServiceControlDescriptor OPTIONAL,
  ...
}

H248SignalsDescriptor ::= SEQUENCE {
  ...
}

ServiceControlDescriptor ::= CHOICE {
  url                       IA5String(SIZE(1..512)),
  signal                    H248SignalsDescriptor,
  nonStandard               NonStandardParameter,
  ...
}

CallSpecificInfo ::= SEQUENCE {
  callIdentifier            CallIdentifier,
  conferenceID              ConferenceIdentifier,
  answeredCall              BOOLEAN,
  ...
}

ServiceControlResult ::= CHOICE {
  started                   NULL,
  failed                    NULL,
  stopped                   NULL,
  notAvailable              NULL,
  neededFeatureNotSupported NULL,
  ...
}

-- Reason codes for RAS messages
GatekeeperRejectReason ::= CHOICE {
  resourceUnavailable       NULL,
  terminalExcluded          NULL,
  invalidRevision           NULL,
  undefinedReason           NULL,
  ...
}

RegistrationRejectReason ::= CHOICE {
  discoveryRequired         NULL,
  invalidRevision           NULL,
  invalidCallSignalAddress  NULL,
  invalidRASAddress         NULL,
  duplicateAlias            SEQUENCE OF AliasAddress,
  invalidTerminalType       NULL,
  undefinedReason           NULL,
  transportNotSupported     NULL,
  ...
}

UnregRejectReason ::= CHOICE {
  notCurrentlyRegistered    NULL,
  callInProgress            NULL,
  undefinedReason           NULL,
  ...
}

AdmissionRejectReason ::= CHOICE {
  calledPartyNotRegistered  NULL,
  invalidPermission         NULL,
  requestDenied             NULL,
  undefinedReason           NULL,
  callerNotRegistered       NULL,
  routeCallToGatekeeper     NULL,
  invalidEndpointIdentifier NULL,
  resourceUnavailable       NULL,
  ...
}

BandwidthRejectReason ::= CHOICE {
  notBound                  NULL,
  invalidConferenceID       NULL,
  invalidPermission         NULL,
  insufficientResources     NULL,
  invalidRevision           NULL,
  undefinedReason           NULL,
  ...
}

DisengageReason ::= CHOICE {
  forcedDrop                NULL,
  normalDrop                NULL,
  undefinedReason           NULL,
  ...
}

DisengageRejectReason ::= CHOICE {
  notRegistered             NULL,
  requestToDropOther        NULL,
  undefinedReason           NULL,
  ...
}

LocationRejectReason ::= CHOICE {
  notRegistered             NULL,
  invalidPermission         NULL,
  requestDenied             NULL,
  undefinedReason           NULL,
  resourceUnavailable       NULL,
  ...
}

InfoRequestNakReason ::= CHOICE {
  notRegistered             NULL,
  securityDenial            NULL,
  undefinedReason           NULL,
  ...
}

END
