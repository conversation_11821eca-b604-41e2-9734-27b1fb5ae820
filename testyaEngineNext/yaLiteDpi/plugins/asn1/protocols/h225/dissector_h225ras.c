/*
 * H.225.0 RAS (Registration, Admission and Status) Protocol Dissector
 * Handles UDP port 1719 traffic
 */

#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdbool.h>
#include <stdio.h>

// Include ASN.1 generated headers for RAS messages
#include "asn1_discovery.h"

#define PROTO_NAME "h225ras"

// Forward declarations
static void process_admission_request(precord_t *precord, AdmissionRequest_t *arq);
static void process_admission_confirm(precord_t *precord, AdmissionConfirm_t *acf);
static void process_disengage_request(precord_t *precord, DisengageRequest_t *drq);
static void process_disengage_confirm(precord_t *precord, DisengageConfirm_t *dcf);

// Schema registration function
static int h225ras_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
    /* 注册 schema */
    pschema_t *pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "H.225.0 RAS Protocol");

    // Basic RAS message fields
    pschema_register_field(pschema, "protocol", YA_FT_STRING, "Protocol");
    pschema_register_field(pschema, "message_type", YA_FT_STRING, "Message Type");
    pschema_register_field(pschema, "data_length", YA_FT_UINT32, "Data Length");
    pschema_register_field(pschema, "decode_status", YA_FT_STRING, "Decode Status");
    pschema_register_field(pschema, "decode_error", YA_FT_STRING, "Decode Error");

    // RAS message specific fields
    pschema_register_field(pschema, "request_seq_num", YA_FT_UINT32, "Request Sequence Number");
    pschema_register_field(pschema, "endpoint_identifier", YA_FT_STRING, "Endpoint Identifier");
    pschema_register_field(pschema, "gatekeeper_identifier", YA_FT_STRING, "Gatekeeper Identifier");
    pschema_register_field(pschema, "call_reference_value", YA_FT_UINT32, "Call Reference Value");
    pschema_register_field(pschema, "bandwidth", YA_FT_UINT32, "Bandwidth");
    pschema_register_field(pschema, "reject_reason", YA_FT_STRING, "Reject Reason");
    pschema_register_field(pschema, "disengage_reason", YA_FT_STRING, "Disengage Reason");

    // Error handling fields
    pschema_register_field(pschema, "validation_error", YA_FT_STRING, "Validation Error");
    pschema_register_field(pschema, "asn1_decode_consumed", YA_FT_UINT32, "ASN.1 Decode Consumed Bytes");

    return 0;
}

// Main RAS dissector function
static int h225ras_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
    printf("dissector_h225ras\n");
    // Input validation
    if (!engine) {
        printf("H225RAS: engine is NULL\n");
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }
    if (!session) {
        printf("H225RAS: session is NULL, need to create UDP session\n");
        // For UDP packets, we need to create a session to store records
        // This is a limitation of the current architecture
        // We'll continue processing but won't be able to create records
    }
    if (!mbuf) {
        printf("H225RAS: mbuf is NULL\n");
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // Check minimum length
    if (nxt_mbuf_get_length(mbuf) < 4) {
        printf("H225RAS: Data too short: %d bytes\n", nxt_mbuf_get_length(mbuf));
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    precord_t *precord = NULL;
    bool using_session_record = false;

    if (session) {
        // Try to create a session record first
        precord = nxt_session_create_record(engine, session);
        if (precord) {
            precord_layer_put_new_layer_cache(precord, PROTO_NAME);
            using_session_record = true;
        }
    }

    if (!precord) {
        // Fallback to packet zone record if session record creation failed
        precord = nxt_engine_pktzone_get_precord(engine);
        if (precord) {
            precord_layer_put_new_layer(precord, PROTO_NAME);
            using_session_record = false;
            printf("H225RAS: Using packet zone record as fallback\n");
        }
    }

    const uint8_t *data;
    size_t data_len;

    // Get data from mbuf
    data = nxt_mbuf_get_raw(mbuf, 0);
    data_len = nxt_mbuf_get_length(mbuf);

    // Additional validation
    if (!data || data_len < 4) {
        printf("H225RAS: Invalid data or insufficient length\n");
        if (precord && using_session_record && session) {
            nxt_session_destroy_record(engine, session, precord);
        }
        return 0;
    }

    // Basic record setup
    if (precord) {
        precord_put(precord, "protocol", string, PROTO_NAME);
        precord_put(precord, "data_length", uinteger, (uint32_t)data_len);
    }

    printf("H225RAS: Attempting to decode %zu bytes of data\n", data_len);
    if (data_len > 0) {
        printf("H225RAS: First few bytes: %02x %02x %02x %02x\n",
               data[0], data_len > 1 ? data[1] : 0,
               data_len > 2 ? data[2] : 0, data_len > 3 ? data[3] : 0);
    }

    // Try to decode as RAS message using ASN.1
    // First try BER decoding
    RasMessage_t *ras_msg = NULL;
    asn_dec_rval_t decode_result = ber_decode(0, &asn_DEF_RasMessage, (void **)&ras_msg, data, data_len);

    // If BER fails or produces invalid result, try PER decoding
    if (decode_result.code != RC_OK || (ras_msg && ras_msg->present == RasMessage_PR_NOTHING)) {
        printf("H225RAS: BER decode failed or invalid, trying PER decode\n");
        if (ras_msg) {
            ASN_STRUCT_FREE(asn_DEF_RasMessage, ras_msg);
            ras_msg = NULL;
        }
        decode_result = uper_decode(0, &asn_DEF_RasMessage, (void **)&ras_msg, data, data_len * 8, 0, 0);
    }

    // Record decode information
    if (precord) {
        precord_put(precord, "asn1_decode_consumed", uinteger, (uint32_t)decode_result.consumed);
    }

    if (decode_result.code != RC_OK) {
        // RAS decode failed
        const char *error_msg = "Unknown decode error";
        switch (decode_result.code) {
            case RC_WMORE:
                error_msg = "More data needed for complete decode";
                break;
            case RC_FAIL:
                error_msg = "Decode failed - invalid ASN.1 structure";
                break;
            default:
                error_msg = "ASN.1 decode error";
                break;
        }

        printf("H225RAS: ASN.1 decode error: %s (consumed: %zu bytes)\n", error_msg, decode_result.consumed);
        if (precord) {
            precord_put(precord, "decode_status", string, "failed");
        }

        if (ras_msg) {
            ASN_STRUCT_FREE(asn_DEF_RasMessage, ras_msg);
        }
        if (precord && using_session_record && session) {
            nxt_session_destroy_record(engine, session, precord);
        }
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // Validate decoded structure
    if (!ras_msg) {
        printf("H225RAS: Decoded structure is NULL\n");
        if (precord) {
            precord_put(precord, "decode_status", string, "failed");
        }
        if (precord && using_session_record && session) {
            nxt_session_destroy_record(engine, session, precord);
        }
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // Successfully decoded as RAS message
    printf("H225RAS: Successfully decoded RAS message\n");
    if (precord) {
        precord_put(precord, "decode_status", string, "success");
    }

    // Process different RAS message types
    const char* msg_type = "unknown";
    printf("H225RAS: RAS message present value: %d\n", ras_msg->present);
    switch (ras_msg->present) {
        case RasMessage_PR_NOTHING:
            msg_type = "nothing_parsed";
            printf("H225RAS: Warning - ASN.1 parsed successfully but no choice selected\n");
            break;
        case RasMessage_PR_admissionRequest:
            msg_type = "admissionRequest";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
                process_admission_request(precord, &ras_msg->choice.admissionRequest);
            }
            break;
        case RasMessage_PR_admissionConfirm:
            msg_type = "admissionConfirm";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
                process_admission_confirm(precord, &ras_msg->choice.admissionConfirm);
            }
            break;
        case RasMessage_PR_admissionReject:
            msg_type = "admissionReject";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_disengageRequest:
            msg_type = "disengageRequest";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
                process_disengage_request(precord, &ras_msg->choice.disengageRequest);
            }
            break;
        case RasMessage_PR_disengageConfirm:
            msg_type = "disengageConfirm";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
                process_disengage_confirm(precord, &ras_msg->choice.disengageConfirm);
            }
            break;
        case RasMessage_PR_gatekeeperRequest:
            msg_type = "gatekeeperRequest";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_gatekeeperConfirm:
            msg_type = "gatekeeperConfirm";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_registrationRequest:
            msg_type = "registrationRequest";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_registrationConfirm:
            msg_type = "registrationConfirm";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_bandwidthRequest:
            msg_type = "bandwidthRequest";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_bandwidthConfirm:
            msg_type = "bandwidthConfirm";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_locationRequest:
            msg_type = "locationRequest";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_locationConfirm:
            msg_type = "locationConfirm";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_infoRequest:
            msg_type = "infoRequest";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        case RasMessage_PR_infoRequestResponse:
            msg_type = "infoRequestResponse";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
        default:
            msg_type = "unknown";
            if (precord) {
                precord_put(precord, "message_type", string, msg_type);
            }
            break;
    }

    printf("H225RAS: Decoded message type: %s\n", msg_type);

    // Clean up ASN.1 structure
    ASN_STRUCT_FREE(asn_DEF_RasMessage, ras_msg);

    if (precord && using_session_record && session) {
        nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);
        nxt_session_destroy_record(engine, session, precord);
    }
    return (int)data_len;
}

// H.225 RAS dissector definition
static nxt_dissector_def_t gDissectorDef =
{
    .name         = PROTO_NAME,
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = h225ras_schema_reg,
    .dissectFun   = h225ras_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_NUMBER("udp", 1719),  // H.225.0 RAS port
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(h225ras)
{
    nxt_dissector_register(&gDissectorDef);
}

// RAS message processing functions
static void process_admission_request(precord_t *precord, AdmissionRequest_t *arq) {
    if (!arq || !precord) return;

    // Extract request sequence number
    precord_put(precord, "request_seq_num", uinteger, (uint32_t)arq->requestSeqNum);

    // Extract endpoint identifier
    if (arq->endpointIdentifier.buf) {
        char *endpoint_id = strndup((char*)arq->endpointIdentifier.buf, arq->endpointIdentifier.size);
        if (endpoint_id) {
            precord_put(precord, "endpoint_identifier", string, endpoint_id);
            free(endpoint_id);
        }
    }

    // Extract call reference value
    precord_put(precord, "call_reference_value", uinteger, (uint32_t)arq->callReferenceValue);

    // Extract bandwidth
    precord_put(precord, "bandwidth", uinteger, (uint32_t)arq->bandWidth);
}

static void process_admission_confirm(precord_t *precord, AdmissionConfirm_t *acf) {
    if (!acf || !precord) return;

    // Extract request sequence number
    precord_put(precord, "request_seq_num", uinteger, (uint32_t)acf->requestSeqNum);

    // Extract bandwidth
    precord_put(precord, "bandwidth", uinteger, (uint32_t)acf->bandWidth);
}

static void process_disengage_request(precord_t *precord, DisengageRequest_t *drq) {
    if (!drq || !precord) return;

    // Extract request sequence number
    precord_put(precord, "request_seq_num", uinteger, (uint32_t)drq->requestSeqNum);

    // Extract endpoint identifier
    if (drq->endpointIdentifier.buf) {
        char *endpoint_id = strndup((char*)drq->endpointIdentifier.buf, drq->endpointIdentifier.size);
        if (endpoint_id) {
            precord_put(precord, "endpoint_identifier", string, endpoint_id);
            free(endpoint_id);
        }
    }

    // Extract call reference value
    precord_put(precord, "call_reference_value", uinteger, (uint32_t)drq->callReferenceValue);

    // Extract disengage reason
    switch (drq->disengageReason.present) {
        case DisengageReason_PR_forcedDrop:
            precord_put(precord, "disengage_reason", string, "forcedDrop");
            break;
        case DisengageReason_PR_normalDrop:
            precord_put(precord, "disengage_reason", string, "normalDrop");
            break;
        case DisengageReason_PR_undefinedReason:
            precord_put(precord, "disengage_reason", string, "undefinedReason");
            break;
        default:
            precord_put(precord, "disengage_reason", string, "unknown");
            break;
    }
}

static void process_disengage_confirm(precord_t *precord, DisengageConfirm_t *dcf) {
    if (!dcf || !precord) return;

    // Extract request sequence number
    precord_put(precord, "request_seq_num", uinteger, (uint32_t)dcf->requestSeqNum);
}
