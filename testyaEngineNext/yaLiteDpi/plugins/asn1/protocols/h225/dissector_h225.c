#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <string.h>
#include <stdlib.h>
#include <stdbool.h>
#include <stdio.h>

// Include ASN.1 generated headers
#include "asn1_discovery.h"

#define PROTO_NAME "h225"

// Schema registration function
static int h225_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db) {
    /* 注册 schema */
    pschema_t *pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "H.225 Protocol");

    // Basic H.225 message fields
    pschema_register_field(pschema, "protocol", YA_FT_STRING, "Protocol");
    pschema_register_field(pschema, "version", YA_FT_STRING, "Version");
    pschema_register_field(pschema, "message_type", YA_FT_STRING, "Message Type");
    pschema_register_field(pschema, "protocol_type", YA_FT_STRING, "Protocol Type"); // RAS or CS
    pschema_register_field(pschema, "data_length", YA_FT_UINT32, "Data Length");
    pschema_register_field(pschema, "decode_status", YA_FT_STRING, "Decode Status");
    pschema_register_field(pschema, "decode_error", YA_FT_STRING, "Decode Error");

    // H.323 User Information fields
    pschema_register_field(pschema, "h323_uu_pdu_present", YA_FT_UINT32, "H323-UU-PDU Present");
    pschema_register_field(pschema, "user_data_present", YA_FT_UINT32, "User Data Present");

    // H.323 UU-PDU fields
    pschema_register_field(pschema, "h323_message_body_type", YA_FT_STRING, "H323 Message Body Type");
    pschema_register_field(pschema, "h245_tunnelling", YA_FT_UINT32, "H245 Tunnelling");
    pschema_register_field(pschema, "h245_control_present", YA_FT_UINT32, "H245 Control Present");
    pschema_register_field(pschema, "h245_control_count", YA_FT_UINT32, "H245 Control Count");
    pschema_register_field(pschema, "nonstandard_data_present", YA_FT_UINT32, "Non-standard Data Present");

    // Protocol identifier
    pschema_register_field(pschema, "protocol_identifier", YA_FT_STRING, "Protocol Identifier");

    // Call information
    pschema_register_field(pschema, "call_identifier", YA_FT_STRING, "Call Identifier");
    pschema_register_field(pschema, "conference_id", YA_FT_STRING, "Conference ID");

    // Endpoint information
    pschema_register_field(pschema, "source_info_present", YA_FT_UINT32, "Source Info Present");
    pschema_register_field(pschema, "dest_info_present", YA_FT_UINT32, "Destination Info Present");
    pschema_register_field(pschema, "dest_call_signalling_address", YA_FT_STRING, "Destination Call Signalling Address");

    // Setup specific fields
    pschema_register_field(pschema, "source_address", YA_FT_STRING, "Source Address");
    pschema_register_field(pschema, "dest_address", YA_FT_STRING, "Destination Address");
    pschema_register_field(pschema, "dest_extra_calling", YA_FT_STRING, "Destination Extra Calling");
    pschema_register_field(pschema, "dest_extra_call_info", YA_FT_STRING, "Destination Extra Call Info");

    // Facility specific fields
    pschema_register_field(pschema, "facility_reason", YA_FT_STRING, "Facility Reason");

    // Release complete specific fields
    pschema_register_field(pschema, "release_complete_reason", YA_FT_STRING, "Release Complete Reason");

    // H.245 tunnelling fields
    pschema_register_field(pschema, "h245_control_0_size", YA_FT_UINT32, "H245 Control 0 Size");
    pschema_register_field(pschema, "h245_control_0_sample", YA_FT_BYTES, "H245 Control 0 Sample");
    pschema_register_field(pschema, "h245_control_1_size", YA_FT_UINT32, "H245 Control 1 Size");
    pschema_register_field(pschema, "h245_control_1_sample", YA_FT_BYTES, "H245 Control 1 Sample");
    pschema_register_field(pschema, "h245_control_2_size", YA_FT_UINT32, "H245 Control 2 Size");
    pschema_register_field(pschema, "h245_control_2_sample", YA_FT_BYTES, "H245 Control 2 Sample");

    // Setup specific boolean fields
    pschema_register_field(pschema, "active_mc", YA_FT_UINT32, "Active MC");
    pschema_register_field(pschema, "media_wait_for_connect", YA_FT_UINT32, "Media Wait For Connect");
    pschema_register_field(pschema, "can_overlap_send", YA_FT_UINT32, "Can Overlap Send");

    // RAS message specific fields
    pschema_register_field(pschema, "request_seq_num", YA_FT_UINT32, "Request Sequence Number");
    pschema_register_field(pschema, "endpoint_identifier", YA_FT_STRING, "Endpoint Identifier");
    pschema_register_field(pschema, "gatekeeper_identifier", YA_FT_STRING, "Gatekeeper Identifier");
    pschema_register_field(pschema, "call_reference_value", YA_FT_UINT32, "Call Reference Value");
    pschema_register_field(pschema, "bandwidth", YA_FT_UINT32, "Bandwidth");
    pschema_register_field(pschema, "reject_reason", YA_FT_STRING, "Reject Reason");
    pschema_register_field(pschema, "disengage_reason", YA_FT_STRING, "Disengage Reason");

    // Error handling and validation fields
    pschema_register_field(pschema, "validation_error", YA_FT_STRING, "Validation Error");
    pschema_register_field(pschema, "asn1_decode_consumed", YA_FT_UINT32, "ASN.1 Decode Consumed Bytes");
    pschema_register_field(pschema, "user_user_ie_offset", YA_FT_UINT32, "User-User IE Offset");
    pschema_register_field(pschema, "user_user_ie_length", YA_FT_UINT32, "User-User IE Length");
    pschema_register_field(pschema, "parse_warnings", YA_FT_STRING, "Parse Warnings");

    return 0;
}

// Helper function to extract OID string
static char* extract_oid_string(OBJECT_IDENTIFIER_t *oid) {
    if (!oid || !oid->buf || oid->size == 0) {
        return NULL;
    }

    static char oid_str[256];
    memset(oid_str, 0, sizeof(oid_str));

    // Use asn_fprint to convert OID to string
    FILE *fp = fmemopen(oid_str, sizeof(oid_str) - 1, "w");
    if (fp) {
        int ret = asn_fprint(fp, &asn_DEF_OBJECT_IDENTIFIER, oid);
        fclose(fp);
        if (ret > 0 && strlen(oid_str) > 0) {
            return oid_str;
        }
    }
    return NULL;
}



// Helper function to extract GUID string
// Temporarily commented out to avoid unused function warning
/*
static char* extract_guid_string(GloballyUniqueID_t *guid) {
    if (!guid || !guid->buf || guid->size != 16) {
        return NULL;
    }

    static char guid_str[64];
    uint8_t *bytes = guid->buf;

    // Additional validation - check for reasonable GUID values
    bool all_zero = true;
    bool all_ff = true;
    for (int i = 0; i < 16; i++) {
        if (bytes[i] != 0x00) all_zero = false;
        if (bytes[i] != 0xFF) all_ff = false;
    }

    // Warn about suspicious GUID values but still process them
    if (all_zero || all_ff) {
        // These might be placeholder or invalid GUIDs, but we'll still format them
    }

    int ret = snprintf(guid_str, sizeof(guid_str),
                      "%02x%02x%02x%02x-%02x%02x-%02x%02x-%02x%02x-%02x%02x%02x%02x%02x%02x",
                      bytes[0], bytes[1], bytes[2], bytes[3],
                      bytes[4], bytes[5], bytes[6], bytes[7],
                      bytes[8], bytes[9], bytes[10], bytes[11],
                      bytes[12], bytes[13], bytes[14], bytes[15]);

    if (ret > 0 && ret < (int)sizeof(guid_str)) {
        return guid_str;
    }

    return NULL;
}
*/

// Helper function to extract alias address
// Temporarily commented out to avoid unused function warning
/*
static char* extract_alias_address(struct AliasAddress *alias) {
    if (!alias) return NULL;

    static char alias_str[256];
    memset(alias_str, 0, sizeof(alias_str));

    switch (alias->present) {
        case AliasAddress_PR_dialledDigits:
            if (alias->choice.dialledDigits.buf && alias->choice.dialledDigits.size > 0 &&
                alias->choice.dialledDigits.size < 128) {  // Reasonable size limit
                snprintf(alias_str, sizeof(alias_str), "dialledDigits:%.*s",
                        (int)alias->choice.dialledDigits.size, alias->choice.dialledDigits.buf);
                return alias_str;
            }
            break;
        case AliasAddress_PR_h323_ID:
            if (alias->choice.h323_ID.buf && alias->choice.h323_ID.size > 0 &&
                alias->choice.h323_ID.size < 128) {  // Reasonable size limit
                snprintf(alias_str, sizeof(alias_str), "h323_ID:%.*s",
                        (int)alias->choice.h323_ID.size, alias->choice.h323_ID.buf);
                return alias_str;
            }
            break;
        case AliasAddress_PR_url_ID:
            if (alias->choice.url_ID.buf && alias->choice.url_ID.size > 0 &&
                alias->choice.url_ID.size < 128) {  // Reasonable size limit
                snprintf(alias_str, sizeof(alias_str), "url_ID:%.*s",
                        (int)alias->choice.url_ID.size, alias->choice.url_ID.buf);
                return alias_str;
            }
            break;
        case AliasAddress_PR_email_ID:
            if (alias->choice.email_ID.buf && alias->choice.email_ID.size > 0 &&
                alias->choice.email_ID.size < 128) {  // Reasonable size limit
                snprintf(alias_str, sizeof(alias_str), "email_ID:%.*s",
                        (int)alias->choice.email_ID.size, alias->choice.email_ID.buf);
                return alias_str;
            }
            break;
        case AliasAddress_PR_NOTHING:
            snprintf(alias_str, sizeof(alias_str), "empty_alias");
            return alias_str;
        default:
            snprintf(alias_str, sizeof(alias_str), "unknown_type:%d", alias->present);
            return alias_str;
    }
    return NULL;
}
*/

// Helper function to process H.245 control data
// Temporarily commented out to avoid crash
/*
static void process_h245_control(precord_t *precord, nxt_engine_t *engine, struct H323_UU_PDU__h245Control *h245_control) {
    if (!h245_control) return;

    precord_put(precord, "h245_control_present", uinteger, 1);
    precord_put(precord, "h245_control_count", uinteger, (uint32_t)h245_control->list.count);

    // Process each H.245 control message (up to 3 for now)
    for (int i = 0; i < h245_control->list.count && i < 3; i++) {
        OCTET_STRING_t *h245_data = h245_control->list.array[i];
        if (h245_data && h245_data->buf && h245_data->size > 0) {
            // Record H.245 tunnelling info using predefined field names
            size_t sample_size = h245_data->size > 16 ? 16 : h245_data->size;

            switch (i) {
                case 0:
                    precord_put(precord, "h245_control_0_size", uinteger, (uint32_t)h245_data->size);
                    precord_put(precord, "h245_control_0_sample", bytes, h245_data->buf, sample_size);
                    break;
                case 1:
                    precord_put(precord, "h245_control_1_size", uinteger, (uint32_t)h245_data->size);
                    precord_put(precord, "h245_control_1_sample", bytes, h245_data->buf, sample_size);
                    break;
                case 2:
                    precord_put(precord, "h245_control_2_size", uinteger, (uint32_t)h245_data->size);
                    precord_put(precord, "h245_control_2_sample", bytes, h245_data->buf, sample_size);
                    break;
            }
        }
    }

    // If H.245 control data is present, set handoff for potential H.245 processing
    if (h245_control->list.count > 0) {
        // Set handoff key for H.245 dissector
        nxt_handoff_set_key_of_number(engine, 2); // 2 = h245
    }
}
*/

// Helper function to process Setup-UUIE specific fields
// Temporarily commented out to avoid crash
/*
static void process_setup_uuie(precord_t *precord, Setup_UUIE_t *setup) {
    if (!setup) return;

    // Extract conference ID
    if (setup->conferenceID.buf && setup->conferenceID.size == 16) {
        char *conf_id = extract_guid_string(&setup->conferenceID);
        if (conf_id) {
            precord_put(precord, "conference_id", string, conf_id);
        }
    }

    // Extract call identifier
    if (setup->callIdentifier.guid.buf && setup->callIdentifier.guid.size == 16) {
        char *call_id = extract_guid_string(&setup->callIdentifier.guid);
        if (call_id) {
            precord_put(precord, "call_identifier", string, call_id);
        }
    }

    // Extract source address
    if (setup->sourceAddress && setup->sourceAddress->list.count > 0) {
        precord_put(precord, "source_info_present", uinteger, 1);
        for (int i = 0; i < setup->sourceAddress->list.count; i++) {
            char *addr = extract_alias_address(setup->sourceAddress->list.array[i]);
            if (addr) {
                precord_put(precord, "source_address", string, addr);
                break; // Just take the first one for now
            }
        }
    }

    // Extract destination address
    if (setup->destinationAddress && setup->destinationAddress->list.count > 0) {
        precord_put(precord, "dest_info_present", uinteger, 1);
        for (int i = 0; i < setup->destinationAddress->list.count; i++) {
            char *addr = extract_alias_address(setup->destinationAddress->list.array[i]);
            if (addr) {
                precord_put(precord, "dest_address", string, addr);
                break; // Just take the first one for now
            }
        }
    }

    // Extract boolean flags
    precord_put(precord, "active_mc", uinteger, setup->activeMC ? 1 : 0);
    precord_put(precord, "media_wait_for_connect", uinteger, setup->mediaWaitForConnect ? 1 : 0);
    precord_put(precord, "can_overlap_send", uinteger, setup->canOverlapSend ? 1 : 0);
    precord_put(precord, "multiple_calls", uinteger, setup->multipleCalls ? 1 : 0);
    precord_put(precord, "maintain_connection", uinteger, setup->maintainConnection ? 1 : 0);
}
*/

// Helper function to process Facility-UUIE specific fields
// Temporarily commented out to avoid crash
/*
static void process_facility_uuie(precord_t *precord, Facility_UUIE_t *facility) {
    if (!facility) return;

    // Extract facility reason
    switch (facility->reason.present) {
        case FacilityReason_PR_routeCallToGatekeeper:
            precord_put(precord, "facility_reason", string, "routeCallToGatekeeper");
            break;
        case FacilityReason_PR_callForwarded:
            precord_put(precord, "facility_reason", string, "callForwarded");
            break;
        case FacilityReason_PR_routeCallToMC:
            precord_put(precord, "facility_reason", string, "routeCallToMC");
            break;
        case FacilityReason_PR_undefinedReason:
            precord_put(precord, "facility_reason", string, "undefinedReason");
            break;
        default:
            precord_put(precord, "facility_reason", string, "unknown");
            break;
    }

    // Extract conference ID if present
    if (facility->conferenceID && facility->conferenceID->buf && facility->conferenceID->size == 16) {
        char *conf_id = extract_guid_string(facility->conferenceID);
        if (conf_id) {
            precord_put(precord, "conference_id", string, conf_id);
        }
    }

    // Extract call identifier
    if (facility->callIdentifier.guid.buf && facility->callIdentifier.guid.size == 16) {
        char *call_id = extract_guid_string(&facility->callIdentifier.guid);
        if (call_id) {
            precord_put(precord, "call_identifier", string, call_id);
        }
    }

    // Extract boolean flags
    precord_put(precord, "multiple_calls", uinteger, facility->multipleCalls ? 1 : 0);
    precord_put(precord, "maintain_connection", uinteger, facility->maintainConnection ? 1 : 0);
}
*/

// Forward declarations for CS message processing
static int process_cs_message(precord_t *precord, nxt_engine_t *engine, const uint8_t *data, size_t data_len);

// Main dissector function
static int h225_dissect(nxt_engine_t *engine, nxt_session_t *session, nxt_mbuf_t *mbuf)
{
    printf("dissector_h225\n");
    // Input validation
    if (!engine || !session || !mbuf) {
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    // Check minimum length
    if (nxt_mbuf_get_length(mbuf) < 4) {
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    precord_t *precord = nxt_session_create_record(engine, session);
    if (!precord) {
        return NXT_DISSECT_ST_VERIFY_FAILED;
    }

    precord_layer_put_new_layer_cache(precord, PROTO_NAME);

    const uint8_t *data;
    size_t data_len;

    // Get data from mbuf
    data = nxt_mbuf_get_raw(mbuf, 0);
    data_len = nxt_mbuf_get_length(mbuf);

    // Additional validation
    if (!data || data_len < 4) {
        printf("H225: Invalid data or insufficient length\n");
        nxt_session_destroy_record(engine, session, precord);
        return 0;
    }

    // Basic record setup
    precord_put(precord, "protocol", string, PROTO_NAME);
    precord_put(precord, "data_length", uinteger, (uint32_t)data_len);

    // This function is called in two scenarios:
    // 1. Directly from UDP port 1719 (RAS messages) - data is direct ASN.1 RasMessage
    // 2. From Q.931 handoff (CS messages) - data is Q.931 User-User IE containing H.323-UserInformation

    // For now, we assume this is always a CS message from Q.931 handoff
    // TODO: Implement proper detection when we have separate RAS dissector for UDP 1719

    int cs_result = process_cs_message(precord, engine, data, data_len);
    if (cs_result > 0) {
        // Successfully parsed as CS
        precord_put(precord, "protocol_type", string, "CS");
        precord_put(precord, "decode_status", string, "success");
        nxt_session_post_event(engine, session, NXT_EVENT_PACKET_MESSAGE, mbuf, precord);
        nxt_session_destroy_record(engine, session, precord);
        return cs_result;
    }

    // CS parsing failed
    printf("H225: Failed to parse CS message\n");
    precord_put(precord, "decode_status", string, "failed");
    nxt_session_destroy_record(engine, session, precord);
    return NXT_DISSECT_ST_VERIFY_FAILED;
}

// Process CS message (Q.931 with User-User IE)
static int process_cs_message(precord_t *precord, nxt_engine_t *engine, const uint8_t *data, size_t data_len) {
    // The data should start with User-User IE (0x7E) from Q.931 handoff
    const uint8_t *h225_data = NULL;
    size_t h225_len = 0;
    bool found_user_user = false;

    printf("H225: Processing CS message, data_len=%zu\n", data_len);
    if (data_len > 0) {
        printf("H225: First few bytes: %02x %02x %02x %02x\n",
               data[0], data_len > 1 ? data[1] : 0,
               data_len > 2 ? data[2] : 0, data_len > 3 ? data[3] : 0);
    }

    // Check if data starts with User-User IE (0x7E)
    if (data_len >= 5 && data[0] == 0x7E) {
        uint16_t ie_len = (data[1] << 8) | data[2];  // User-User IE uses 2-byte length

        printf("H225: Found User-User IE, length=%d\n", ie_len);

        // Validate IE length
        if (ie_len > 0 && ie_len <= data_len - 3) {
            // Check for H.225 protocol discriminator (0x05)
            if (data[3] == 0x05) {
                h225_data = data + 4;  // Skip IE ID, 2-byte length, and protocol discriminator
                h225_len = ie_len - 1; // Subtract protocol discriminator byte
                found_user_user = true;
                printf("H225: H.225 protocol discriminator found, h225_len=%zu\n", h225_len);
            } else {
                printf("H225: Found User-User IE but not H.225 protocol (got 0x%02x)\n", data[3]);
            }
        } else {
            printf("H225: Invalid User-User IE length: %d (data_len=%zu)\n", ie_len, data_len);
        }
    } else {
        // Try to find User-User IE in the data
        for (size_t i = 0; i < data_len - 4; i++) {
            if (data[i] == 0x7E) {
                uint16_t ie_len = (data[i + 1] << 8) | data[i + 2];

                if (ie_len > 0 && ie_len <= data_len - i - 3) {
                    if (data[i + 3] == 0x05) {
                        h225_data = data + i + 4;
                        h225_len = ie_len - 1;
                        found_user_user = true;
                        printf("H225: Found User-User IE at offset %zu, h225_len=%zu\n", i, h225_len);
                        break;
                    }
                }
            }
        }
    }

    if (!found_user_user || !h225_data) {
        printf("H225: No valid User-User IE found\n");
        return 0; // Not a CS message
    }

    // Record User-User IE information
    precord_put(precord, "user_user_ie_length", uinteger, (uint32_t)h225_len);

    // Validate H.225 data length
    if (h225_len == 0) {
        printf("H225: Empty H.225 data\n");
        return 0;
    }

    if (h225_len > 65535) {  // Reasonable upper limit
        printf("H225: H.225 data too large: %zu\n", h225_len);
        return 0;
    }

    // Decode H.323 User Information using ASN.1
    // H.225 typically uses PER encoding, try PER first then BER
    H323_UserInformation_t *h323_ui = NULL;
    asn_dec_rval_t decode_result;

    printf("H225: Attempting PER decode of %zu bytes\n", h225_len);
    if (h225_len > 0) {
        printf("H225: H.225 data first few bytes: %02x %02x %02x %02x\n",
               h225_data[0], h225_len > 1 ? h225_data[1] : 0,
               h225_len > 2 ? h225_data[2] : 0, h225_len > 3 ? h225_data[3] : 0);
    }

    // Try multiple PER decode approaches based on data patterns
    // Analyze the first few bytes to determine the best decode strategy
    bool decode_success = false;
    int successful_offset = -1;

    printf("H225: Analyzing data pattern for decode strategy\n");
    if (h225_len >= 4) {
        printf("H225: First 4 bytes: %02x %02x %02x %02x\n",
               h225_data[0], h225_data[1], h225_data[2], h225_data[3]);
    }

    // Force 0-byte offset for all messages to match tshark behavior
    int preferred_offset = 0;
    if (h225_len >= 1) {
        uint8_t first_byte = h225_data[0];
        printf("H225: Based on first byte 0x%02x, forcing %d-byte offset to match tshark\n", first_byte, preferred_offset);
    }

    // Try preferred offset first, then fallback to other offsets
    int offsets_to_try[] = {preferred_offset, 0, 1, 2, 3};
    int num_offsets = sizeof(offsets_to_try) / sizeof(offsets_to_try[0]);

    for (int i = 0; i < num_offsets && !decode_success; i++) {
        int offset = offsets_to_try[i];
        if (h225_len <= (size_t)offset) continue;

        // Skip if we already tried this offset
        if (i > 0 && offset == preferred_offset) continue;

        printf("H225: Trying %d-byte offset PER decode\n", offset);
        decode_result = uper_decode_complete(NULL, &asn_DEF_H323_UserInformation,
                                           (void **)&h323_ui, h225_data + offset, h225_len - offset);

        if (decode_result.code == RC_OK && h323_ui) {
            printf("H225: %d-byte offset PER decode successful\n", offset);
            decode_success = true;
            successful_offset = offset;
        } else {
            printf("H225: %d-byte offset PER decode failed\n", offset);
            if (h323_ui) {
                ASN_STRUCT_FREE(asn_DEF_H323_UserInformation, h323_ui);
                h323_ui = NULL;
            }
        }
    }

    if (decode_success) {
        printf("H225: Successfully decoded with %d-byte offset\n", successful_offset);
    }

    // Record decode information
    precord_put(precord, "asn1_decode_consumed", uinteger, (uint32_t)decode_result.consumed);

    if (decode_result.code != RC_OK) {
        // Provide detailed error information
        const char *error_msg = "Unknown decode error";
        switch (decode_result.code) {
            case RC_WMORE:
                error_msg = "More data needed for complete decode";
                break;
            case RC_FAIL:
                error_msg = "Decode failed - invalid ASN.1 structure";
                break;
            default:
                error_msg = "ASN.1 decode error";
                break;
        }

        printf("H225: ASN.1 decode error: %s (consumed: %zu bytes)\n", error_msg, decode_result.consumed);
        return 0;
    }

    // Validate decoded structure
    if (!h323_ui) {
        printf("H225: Decoded structure is NULL\n");
        return 0;
    }

    printf("H225: Successfully decoded H323_UserInformation\n");

    // Debug: Print the entire ASN.1 structure
    printf("H225: Printing decoded ASN.1 structure:\n");
    asn_fprint(stdout, &asn_DEF_H323_UserInformation, h323_ui);
    printf("H225: End of ASN.1 structure\n");

    // H323-UU-PDU is always present in H323_UserInformation
    precord_put(precord, "h323_uu_pdu_present", uinteger, 1);

    H323_UU_PDU_t *uu_pdu = &h323_ui->h323_uu_pdu;

    printf("H225: Processing H323_UU_PDU\n");

    // Extract H.245 tunnelling information
    if (uu_pdu->h245Tunnelling) {
        // ASN.1 BOOLEAN is represented as long in asn1c
        long tunnelling_value = *uu_pdu->h245Tunnelling;
        int h245_tunnelling = (tunnelling_value != 0) ? 1 : 0;
        precord_put(precord, "h245_tunnelling", uinteger, h245_tunnelling);
        printf("H225: H.245 tunnelling: %s (raw value: %ld)\n", h245_tunnelling ? "enabled" : "disabled", tunnelling_value);

        // If H.245 tunnelling is enabled, set handoff for H.245 processing
        if (tunnelling_value != 0) {
            nxt_handoff_set_key_of_number(engine, 2); // 2 = h245
            printf("H225: Setting handoff to H.245 due to tunnelling\n");
        }
    } else {
        // According to H.323 standard, h245Tunnelling should always be present
        // If it's not present, assume it's false (disabled)
        printf("H225: H.245 tunnelling field not present, assuming disabled\n");
        precord_put(precord, "h245_tunnelling", uinteger, 0);
    }

    // Process H.245 control data if present
    if (uu_pdu->h245Control) {
        printf("H225: Processing H.245 control data\n");
        // Temporarily comment out to avoid crash
        // process_h245_control(precord, engine, uu_pdu->h245Control);
        precord_put(precord, "h245_control_present", uinteger, 1);
    } else {
        printf("H225: No H.245 control data present\n");
        precord_put(precord, "h245_control_present", uinteger, 0);
    }

    // Extract protocol identifier based on message type
    if (uu_pdu->h323_message_body.present == H323_UU_PDU__h323_message_body_PR_setup) {
        Setup_UUIE_t *setup = &uu_pdu->h323_message_body.choice.setup;
        char *oid_str = extract_oid_string(&setup->protocolIdentifier);
        if (oid_str) {
            precord_put(precord, "protocol_identifier", string, oid_str);
        }
    }

    // Determine message type and extract specific fields
    printf("H225: H.323 message body present value: %d\n", uu_pdu->h323_message_body.present);
    printf("H225: Enum values - NOTHING:%d, setup:%d, callProceeding:%d, connect:%d, alerting:%d, facility:%d, empty:%d\n",
           H323_UU_PDU__h323_message_body_PR_NOTHING,
           H323_UU_PDU__h323_message_body_PR_setup,
           H323_UU_PDU__h323_message_body_PR_callProceeding,
           H323_UU_PDU__h323_message_body_PR_connect,
           H323_UU_PDU__h323_message_body_PR_alerting,
           H323_UU_PDU__h323_message_body_PR_facility,
           H323_UU_PDU__h323_message_body_PR_empty);

    // Map the present value to the expected ASN.1 encoding value for comparison with tshark
    int asn1_encoded_value = uu_pdu->h323_message_body.present - 1; // Subtract 1 to account for NOTHING
    printf("H225: Expected ASN.1 encoded value (for tshark comparison): %d\n", asn1_encoded_value);

    switch (uu_pdu->h323_message_body.present) {
        case H323_UU_PDU__h323_message_body_PR_setup:
            precord_put(precord, "h323_message_body_type", string, "setup");
            precord_put(precord, "message_type", string, "setup");
            printf("H225: Message type: setup\n");
            // Temporarily comment out to avoid crash
            // process_setup_uuie(precord, &uu_pdu->h323_message_body.choice.setup);
            break;
        case H323_UU_PDU__h323_message_body_PR_callProceeding:
            precord_put(precord, "h323_message_body_type", string, "callProceeding");
            precord_put(precord, "message_type", string, "callProceeding");
            printf("H225: Message type: callProceeding\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_connect:
            precord_put(precord, "h323_message_body_type", string, "connect");
            precord_put(precord, "message_type", string, "connect");
            printf("H225: Message type: connect\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_alerting:
            precord_put(precord, "h323_message_body_type", string, "alerting");
            precord_put(precord, "message_type", string, "alerting");
            printf("H225: Message type: alerting\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_information:
            precord_put(precord, "h323_message_body_type", string, "information");
            precord_put(precord, "message_type", string, "information");
            printf("H225: Message type: information\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_releaseComplete:
            precord_put(precord, "h323_message_body_type", string, "releaseComplete");
            precord_put(precord, "message_type", string, "releaseComplete");
            printf("H225: Message type: releaseComplete\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_facility:
            precord_put(precord, "h323_message_body_type", string, "facility");
            precord_put(precord, "message_type", string, "facility");
            printf("H225: Message type: facility\n");
            // Temporarily comment out to avoid crash
            // process_facility_uuie(precord, &uu_pdu->h323_message_body.choice.facility);
            break;
        case H323_UU_PDU__h323_message_body_PR_progress:
            precord_put(precord, "h323_message_body_type", string, "progress");
            precord_put(precord, "message_type", string, "progress");
            printf("H225: Message type: progress\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_empty:
            precord_put(precord, "h323_message_body_type", string, "empty");
            precord_put(precord, "message_type", string, "empty");
            printf("H225: Message type: empty\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_status:
            precord_put(precord, "h323_message_body_type", string, "status");
            precord_put(precord, "message_type", string, "status");
            printf("H225: Message type: status\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_statusInquiry:
            precord_put(precord, "h323_message_body_type", string, "statusInquiry");
            precord_put(precord, "message_type", string, "statusInquiry");
            printf("H225: Message type: statusInquiry\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_setupAcknowledge:
            precord_put(precord, "h323_message_body_type", string, "setupAcknowledge");
            precord_put(precord, "message_type", string, "setupAcknowledge");
            printf("H225: Message type: setupAcknowledge\n");
            break;
        case H323_UU_PDU__h323_message_body_PR_notify:
            precord_put(precord, "h323_message_body_type", string, "notify");
            precord_put(precord, "message_type", string, "notify");
            printf("H225: Message type: notify\n");
            break;
        default:
            precord_put(precord, "h323_message_body_type", string, "unknown");
            precord_put(precord, "message_type", string, "unknown");
            printf("H225: Message type: unknown (%d)\n", uu_pdu->h323_message_body.present);
            break;
    }

    // Check if user data is present
    if (h323_ui->user_data) {
        precord_put(precord, "user_data_present", uinteger, 1);
    } else {
        precord_put(precord, "user_data_present", uinteger, 0);
    }

    // Clean up ASN.1 structure
    ASN_STRUCT_FREE(asn_DEF_H323_UserInformation, h323_ui);

    return (int)data_len; // Consume all data
}

// H.225 dissector definition
static nxt_dissector_def_t gDissectorDef =
{
    .name         = PROTO_NAME,
    .type         = NXT_DISSECTOR_TYPE_APP,
    .schemaRegFun = h225_schema_reg,
    .dissectFun   = h225_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(h225)
{
    nxt_dissector_register(&gDissectorDef);

    // Register handoff rule to receive calls from Q.931 (key 1) for CS messages
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "q931", 1, "h225");

    // Register handoff rule for H.245 processing (key 2)
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "h225", 2, "h245");
}


