#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <arpa/inet.h>
#include <string.h>
#include <stdbool.h>

#define PROTO_NAME     "q931"
#define MIN_Q931_HEADER_LEN 3

// Q.931 Protocol Discriminator
#define Q931_PROTOCOL_DISCRIMINATOR 0x08

// Q.931 Message Types
#define Q931_ALERTING           0x01
#define Q931_CALL_PROCEEDING    0x02
#define Q931_CONNECT            0x07
#define Q931_CONNECT_ACK        0x0F
#define Q931_PROGRESS           0x03
#define Q931_SETUP              0x05
#define Q931_SETUP_ACK          0x0D
#define Q931_RESUME             0x26
#define Q931_RESUME_ACK         0x2E
#define Q931_RESUME_REJECT      0x22
#define Q931_SUSPEND            0x25
#define Q931_SUSPEND_ACK        0x2D
#define Q931_SUSPEND_REJECT     0x21
#define Q931_USER_INFORMATION   0x20
#define Q931_DISCONNECT         0x45
#define Q931_RELEASE            0x4D
#define Q931_RELEASE_COMPLETE   0x5A
#define Q931_RESTART            0x46
#define Q931_RESTART_ACK        0x4E
#define Q931_INFORMATION        0x7B
#define Q931_NOTIFY             0x6E
#define Q931_STATUS             0x7D
#define Q931_STATUS_ENQUIRY     0x75
#define Q931_FACILITY           0x62

// Q.931 Information Element IDs
#define Q931_IE_BEARER_CAPABILITY      0x04
#define Q931_IE_CAUSE                  0x08
#define Q931_IE_CALL_IDENTITY          0x10
#define Q931_IE_CALL_STATE             0x14
#define Q931_IE_CHANNEL_IDENTIFICATION 0x18
#define Q931_IE_PROGRESS_INDICATOR     0x1E
#define Q931_IE_NETWORK_SPECIFIC_FAC   0x20
#define Q931_IE_NOTIFICATION_INDICATOR 0x27
#define Q931_IE_DISPLAY                0x28
#define Q931_IE_DATE_TIME              0x29
#define Q931_IE_KEYPAD_FACILITY        0x2C
#define Q931_IE_SIGNAL                 0x34
#define Q931_IE_CALLING_PARTY_NUMBER   0x6C
#define Q931_IE_CALLING_PARTY_SUBADDR  0x6D
#define Q931_IE_CALLED_PARTY_NUMBER    0x70
#define Q931_IE_CALLED_PARTY_SUBADDR   0x71
#define Q931_IE_REDIRECTING_NUMBER     0x74
#define Q931_IE_TRANSIT_NETWORK_SEL    0x78
#define Q931_IE_RESTART_INDICATOR      0x79
#define Q931_IE_LOW_LAYER_COMPAT       0x7C
#define Q931_IE_HIGH_LAYER_COMPAT      0x7D
#define Q931_IE_USER_USER              0x7E
#define Q931_IE_ESCAPE_FOR_EXT         0x7F

// Helper function to get message type name
static const char* get_q931_message_type_name(uint8_t msg_type)
{
    switch (msg_type) {
        case Q931_ALERTING:           return "ALERTING";
        case Q931_CALL_PROCEEDING:    return "CALL_PROCEEDING";
        case Q931_CONNECT:            return "CONNECT";
        case Q931_CONNECT_ACK:        return "CONNECT_ACK";
        case Q931_PROGRESS:           return "PROGRESS";
        case Q931_SETUP:              return "SETUP";
        case Q931_SETUP_ACK:          return "SETUP_ACK";
        case Q931_RESUME:             return "RESUME";
        case Q931_RESUME_ACK:         return "RESUME_ACK";
        case Q931_RESUME_REJECT:      return "RESUME_REJECT";
        case Q931_SUSPEND:            return "SUSPEND";
        case Q931_SUSPEND_ACK:        return "SUSPEND_ACK";
        case Q931_SUSPEND_REJECT:     return "SUSPEND_REJECT";
        case Q931_USER_INFORMATION:   return "USER_INFORMATION";
        case Q931_DISCONNECT:         return "DISCONNECT";
        case Q931_RELEASE:            return "RELEASE";
        case Q931_RELEASE_COMPLETE:   return "RELEASE_COMPLETE";
        case Q931_RESTART:            return "RESTART";
        case Q931_RESTART_ACK:        return "RESTART_ACK";
        case Q931_INFORMATION:        return "INFORMATION";
        case Q931_NOTIFY:             return "NOTIFY";
        case Q931_STATUS:             return "STATUS";
        case Q931_STATUS_ENQUIRY:     return "STATUS_ENQUIRY";
        case Q931_FACILITY:           return "FACILITY";
        default:                      return "UNKNOWN";
    }
}



// Helper function to parse information elements and find User-User IE
static int parse_information_elements(precord_t *precord, nxt_mbuf_t *mbuf, int offset, int remaining_length,
                                     int *user_user_offset, int *user_user_length)
{
    int ie_count = 0;
    int current_offset = offset;
    bool has_user_user_ie = false;
    *user_user_offset = -1;
    *user_user_length = 0;

    while (remaining_length > 0 && ie_count < 10) { // Limit to 10 IEs to avoid excessive processing
        if (remaining_length < 2) {
            break; // Not enough data for IE header
        }

        uint8_t ie_id = nxt_mbuf_get_uint8(mbuf, current_offset);

        // User-User IE uses 2-byte length field, others use 1-byte
        uint16_t ie_length;
        int ie_header_len;

        if (ie_id == Q931_IE_USER_USER) {
            // User-User IE has 2-byte length field
            if (remaining_length < 3) {
                break; // Not enough data for User-User IE header
            }
            ie_length = (nxt_mbuf_get_uint8(mbuf, current_offset + 1) << 8) |
                        nxt_mbuf_get_uint8(mbuf, current_offset + 2);
            ie_header_len = 3; // ID + 2-byte length
        } else {
            // Other IEs have 1-byte length field
            if (remaining_length < 2) {
                break; // Not enough data for IE header
            }
            ie_length = nxt_mbuf_get_uint8(mbuf, current_offset + 1);
            ie_header_len = 2; // ID + 1-byte length
        }

        if (ie_length > remaining_length - ie_header_len) {
            break; // IE length exceeds remaining data
        }

        // Check for User-User IE (0x7E) which contains H.225 data
        if (ie_id == Q931_IE_USER_USER) {
            has_user_user_ie = true;
            *user_user_offset = current_offset + ie_header_len; // Point to IE data
            *user_user_length = ie_length;
            printf("Q931: Found User-User IE at offset %d, length %d\n", *user_user_offset, *user_user_length);
        }

        // Special handling for specific IEs (skip User-User IE as it's handled by H.225)
        if (ie_length > 0 && ie_id != Q931_IE_USER_USER) {
            const uint8_t *ie_data = nxt_mbuf_get_raw(mbuf, current_offset + ie_header_len);

            switch (ie_id) {
                case Q931_IE_CALLING_PARTY_NUMBER:
                case Q931_IE_CALLED_PARTY_NUMBER:
                    if (ie_length >= 2) {
                        // Extract the actual number (skip type/plan byte)
                        char number_str[32];
                        size_t number_len = ie_length - 1;
                        if (number_len > 31) number_len = 31;
                        memcpy(number_str, &ie_data[1], number_len);
                        number_str[number_len] = '\0';

                        if (ie_id == Q931_IE_CALLING_PARTY_NUMBER) {
                            precord_put(precord, "calling_number", string, number_str);
                        } else {
                            precord_put(precord, "called_number", string, number_str);
                        }
                    }
                    break;

                case Q931_IE_DISPLAY:
                    if (ie_length > 0) {
                        char display_str[64];
                        size_t display_len = ie_length > 63 ? 63 : ie_length;
                        memcpy(display_str, ie_data, display_len);
                        display_str[display_len] = '\0';
                        precord_put(precord, "display_text", string, display_str);
                    }
                    break;

                case Q931_IE_CAUSE:
                    if (ie_length >= 2) {
                        // Cause value is in the second byte (bits 0-6)
                        uint8_t cause_value = ie_data[1] & 0x7F;
                        precord_put(precord, "cause_value", uinteger, (uint32_t)cause_value);
                    }
                    break;
            }
        }

        current_offset += ie_header_len + ie_length;
        remaining_length -= (ie_header_len + ie_length);
        ie_count++;
    }

    precord_put(precord, "ie_count", uinteger, (uint32_t)ie_count);
    precord_put(precord, "has_user_user_ie", uinteger, has_user_user_ie ? 1 : 0);
    return current_offset - offset;
}

static int q931_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    printf("Q.931 dissector called with %d bytes\n", nxt_mbuf_get_length(mbuf));

    // Check if we have enough data for Q.931 header
    if (nxt_mbuf_get_length(mbuf) < MIN_Q931_HEADER_LEN) {
        precord_put(precord, "error", string, "Insufficient data for Q.931 header");
        return 0;
    }

    // Extract Q.931 header fields
    uint8_t protocol_discriminator = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t call_reference_length = nxt_mbuf_get_uint8(mbuf, 1);
    
    // Validate protocol discriminator
    if (protocol_discriminator != Q931_PROTOCOL_DISCRIMINATOR) {
        precord_put(precord, "error", string, "Invalid Q.931 protocol discriminator");
        precord_put(precord, "protocol_discriminator", uinteger, (uint32_t)protocol_discriminator);
        return 0;
    }

    // Validate call reference length
    if (call_reference_length > 15) {
        precord_put(precord, "error", string, "Invalid call reference length");
        precord_put(precord, "call_reference_length", uinteger, (uint32_t)call_reference_length);
        return 0;
    }

    int header_length = 2 + call_reference_length + 1; // PD + CRL + CR + MT
    if (nxt_mbuf_get_length(mbuf) < header_length) {
        precord_put(precord, "error", string, "Insufficient data for complete Q.931 header");
        return 0;
    }

    // Store basic header fields
    precord_put(precord, "protocol_discriminator", uinteger, (uint32_t)protocol_discriminator);
    precord_put(precord, "call_reference_length", uinteger, (uint32_t)call_reference_length);

    // Extract call reference value
    uint32_t call_reference = 0;
    bool call_reference_flag = false;
    
    if (call_reference_length > 0) {
        // Extract call reference bytes
        for (int i = 0; i < call_reference_length; i++) {
            uint8_t cr_byte = nxt_mbuf_get_uint8(mbuf, 2 + i);
            if (i == 0) {
                // First byte contains the flag bit
                call_reference_flag = (cr_byte & 0x80) != 0;
                call_reference = (cr_byte & 0x7F);
            } else {
                call_reference = (call_reference << 8) | cr_byte;
            }
        }
    }
    
    precord_put(precord, "call_reference", uinteger, call_reference);
    precord_put(precord, "call_reference_flag", uinteger, call_reference_flag ? 1 : 0);

    // Extract message type
    uint8_t message_type = nxt_mbuf_get_uint8(mbuf, 2 + call_reference_length);
    precord_put(precord, "message_type", uinteger, (uint32_t)message_type);
    precord_put(precord, "message_type_name", string, get_q931_message_type_name(message_type));

    // Parse information elements if present and find User-User IE
    int user_user_offset = -1;
    int user_user_length = 0;
    int remaining_length = nxt_mbuf_get_length(mbuf) - header_length;
    if (remaining_length > 0) {
        parse_information_elements(precord, mbuf, header_length, remaining_length,
                                 &user_user_offset, &user_user_length);
    }

    // Check for H.225 User-User information element (common in H.323)
    if (message_type == Q931_SETUP || message_type == Q931_CONNECT ||
        message_type == Q931_ALERTING || message_type == Q931_CALL_PROCEEDING ||
        message_type == Q931_RELEASE_COMPLETE || message_type == Q931_FACILITY) {

        // These messages often contain H.225 information
        precord_put(precord, "h323_related", uinteger, 1);

        // Set handoff key for H.225 CS processing if User-User IE is present
        if (user_user_offset >= 0 && user_user_length > 0) {
            nxt_handoff_set_key_of_number(engine, 1); // 1 = h225cs
            printf("Q931: Setting handoff to H.225 CS, User-User IE found\n");
        }
    }

    // Return the number of bytes consumed by Q.931
    // If we found a User-User IE, we consume up to the start of that IE
    // The H.225 dissector will process the User-User IE content
    if (user_user_offset >= 0) {
        // Return the offset where User-User IE starts (including the IE header)
        return user_user_offset - 3; // -3 to include IE ID and 2-byte length for User-User IE
    } else {
        // No User-User IE found, consume all Q.931 data
        return nxt_mbuf_get_length(mbuf);
    }
}

static int q931_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "Q.931 Call Control");
    
    // Q.931 header fields
    pschema_register_field(pschema, "protocol_discriminator", YA_FT_UINT32, "Protocol discriminator");
    pschema_register_field(pschema, "call_reference_length", YA_FT_UINT32, "Call reference length");
    pschema_register_field(pschema, "call_reference", YA_FT_UINT32, "Call reference value");
    pschema_register_field(pschema, "call_reference_flag", YA_FT_UINT32, "Call reference flag");
    pschema_register_field(pschema, "message_type", YA_FT_UINT32, "Message type");
    pschema_register_field(pschema, "message_type_name", YA_FT_STRING, "Message type name");
    
    // Information elements
    pschema_register_field(pschema, "ie_count", YA_FT_UINT32, "Number of information elements");
    pschema_register_field(pschema, "has_user_user_ie", YA_FT_UINT32, "Has User-User IE");

    // Specific IE fields
    pschema_register_field(pschema, "calling_number", YA_FT_STRING, "Calling party number");
    pschema_register_field(pschema, "called_number", YA_FT_STRING, "Called party number");
    pschema_register_field(pschema, "display_text", YA_FT_STRING, "Display text");
    pschema_register_field(pschema, "cause_value", YA_FT_UINT32, "Cause value");
    
    // H.323 related fields
    pschema_register_field(pschema, "h323_related", YA_FT_UINT32, "H.323 related message");
    
    // Error fields
    pschema_register_field(pschema, "error", YA_FT_STRING, "Error message");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "q931",
    .type         = NXT_DISSECTOR_TYPE_BEARER,  // Q.931 is a bearer protocol that carries H.225
    .schemaRegFun = q931_schema_reg,
    .dissectFun   = q931_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // Q.931 is typically carried over TPKT
        // It can also be mounted directly on specific protocols
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(q931)
{
    nxt_dissector_register(&gDissectorDef);

    // Register handoff rule for H.225 CS processing
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "q931", 1, "h225");
}
