#include <yaEngineNext/nxt_engine.h>
#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include <yaBasicUtils/macro.h>
#include <stddef.h>
#include <arpa/inet.h>

#define PROTO_NAME     "tpkt"
#define HEADER_LEN_TPKT 4

// Global nesting level tracker (simple approach for demonstration)
static int g_tpkt_nesting_level = 0;

// TPKT header structure (RFC 1006)
// 0                   1                   2                   3
// 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
// |    Version    |   Reserved    |            Length             |
// +-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+

static int tpkt_dissect(nxt_engine_t *engine, nxt_session_t *session _U_, nxt_mbuf_t *mbuf)
{
    precord_t *precord = nxt_engine_pktzone_get_precord(engine);
    precord_layer_put_new_layer(precord, PROTO_NAME);

    // Check if we have enough data for TPKT header
    if (nxt_mbuf_get_length(mbuf) < HEADER_LEN_TPKT) {
        return 0;
    }

    // Detect nesting level by checking if we're being called from H.245
    // This is a simple heuristic - in a real implementation, you might want
    // to pass context information through the engine
    g_tpkt_nesting_level++;

    precord_put(precord, "nesting_level", uinteger, (uint32_t)g_tpkt_nesting_level);

    if (g_tpkt_nesting_level > 1) {
        precord_put(precord, "is_nested", uinteger, 1);
        precord_put(precord, "parent_protocol", string, "h245");
    } else {
        precord_put(precord, "is_nested", uinteger, 0);
        precord_put(precord, "parent_protocol", string, "tcp");
    }

    // Extract TPKT header fields
    uint8_t version = nxt_mbuf_get_uint8(mbuf, 0);
    uint8_t reserved = nxt_mbuf_get_uint8(mbuf, 1);
    uint16_t length = nxt_mbuf_get_uint16_ntoh(mbuf, 2);

    // Validate TPKT header
    if (version != 3) {
        precord_put(precord, "version", uinteger, (uint32_t)version);
        return 0;
    }

    if (reserved != 0) {
        precord_put(precord, "warning", string, "Non-zero reserved field");
    }

    if (length < HEADER_LEN_TPKT) {
        precord_put(precord, "length", uinteger, (uint32_t)length);
        return 0;
    }

    if (length > nxt_mbuf_get_length(mbuf)) {
        precord_put(precord, "length", uinteger, (uint32_t)length);
        precord_put(precord, "packet_size", uinteger, (uint32_t)nxt_mbuf_get_length(mbuf));
        return 0;
    }

    // Store TPKT header fields
    precord_put(precord, "version", uinteger, (uint32_t)version);
    precord_put(precord, "reserved", uinteger, (uint32_t)reserved);
    precord_put(precord, "length", uinteger, (uint32_t)length);
    
    // Calculate payload length
    uint16_t payload_length = length - HEADER_LEN_TPKT;
    precord_put(precord, "payload_length", uinteger, (uint32_t)payload_length);

    // Check if this looks like H.323 data (common use case for TPKT)
    if (payload_length > 0) {
        const uint8_t *payload = nxt_mbuf_get_raw(mbuf, HEADER_LEN_TPKT);
        
        // Check for Q.931 message (H.225 call signalling)
        // Q.931 messages typically start with protocol discriminator 0x08
        if (payload_length >= 1 && payload[0] == 0x08) {
            precord_put(precord, "payload_type", string, "Q.931");
            // Set handoff key for Q.931 dissector
            nxt_handoff_set_key_of_number(engine, 1); // 1 = q931
        }
        // Check for H.245 control messages (typically start with specific ASN.1 tags)
        else if (payload_length >= 1 && (payload[0] == 0x30 || payload[0] == 0xa0)) {
            precord_put(precord, "payload_type", string, "H.245");
            // Set handoff key for H.245 dissector
            nxt_handoff_set_key_of_number(engine, 2); // 2 = h245
        }
        // Check for other common protocols
        else if (payload_length >= 2) {
            // Check for T.120 data (MCS Connect Initial)
            if (payload[0] == 0x7f && payload[1] == 0x65) {
                precord_put(precord, "payload_type", string, "T.120");
            }
            // Check for other ASN.1 encoded data
            else if ((payload[0] & 0x1f) == 0x10 || (payload[0] & 0x1f) == 0x11) {
                precord_put(precord, "payload_type", string, "ASN.1");
            }
            else {
                precord_put(precord, "payload_type", string, "Unknown");
            }
        }
        else {
            precord_put(precord, "payload_type", string, "Unknown");
        }

        // Store first few bytes of payload for analysis
        size_t sample_size = payload_length > 16 ? 16 : payload_length;
        precord_put(precord, "payload_sample", bytes, payload, sample_size);
    }

    // Return the TPKT header length to indicate successful parsing
    // The payload will be handled by the next dissector in the chain

    // Reset nesting level when we're done (this is a simple approach)
    // In a more sophisticated implementation, you'd use proper context tracking
    g_tpkt_nesting_level--;

    return HEADER_LEN_TPKT;
}

static int tpkt_schema_reg(nxt_engine_t *engine _U_, pschema_db_t *db)
{
    /* 注册 schema */
    pschema_t* pschema = pschema_register_proto(db, PRECORD_NOT_IMPORT_TEMPLATE, PROTO_NAME, "TPKT Transport Protocol");
    
    // TPKT header fields
    pschema_register_field(pschema, "version", YA_FT_UINT32, "TPKT version");
    pschema_register_field(pschema, "reserved", YA_FT_UINT32, "Reserved field");
    pschema_register_field(pschema, "length", YA_FT_UINT32, "Total packet length");
    pschema_register_field(pschema, "payload_length", YA_FT_UINT32, "Payload length");
    
    // Payload analysis fields
    pschema_register_field(pschema, "payload_type", YA_FT_STRING, "Detected payload type");
    pschema_register_field(pschema, "payload_sample", YA_FT_BYTES, "First bytes of payload");
    
    // Error and warning fields
    pschema_register_field(pschema, "warning", YA_FT_STRING, "Warning message");
    pschema_register_field(pschema, "packet_size", YA_FT_UINT32, "Actual packet size");

    // Nested structure tracking
    pschema_register_field(pschema, "nesting_level", YA_FT_UINT32, "TPKT nesting level");
    pschema_register_field(pschema, "is_nested", YA_FT_UINT32, "Is nested TPKT");
    pschema_register_field(pschema, "parent_protocol", YA_FT_STRING, "Parent protocol");

    return 0;
}

static nxt_dissector_def_t gDissectorDef =
{
    .name         = "tpkt",
    .type         = NXT_DISSECTOR_TYPE_BEARER,
    .schemaRegFun = tpkt_schema_reg,
    .dissectFun   = tpkt_dissect,
    .handoff      = NXT_HANDOFF_DEFAULT,
    .mountAt      = {
        // TPKT is commonly used on these ports for H.323
        // Use PORT_PAYLOAD to register with TCP dissector, NULL payload means match any payload on this port
        NXT_MNT_PORT_PAYLOAD("tcp", 1720, NULL),  // H.225 call signalling
        NXT_MNT_PORT_PAYLOAD("tcp", 1503, NULL),  // T.120 data sharing
        NXT_MNT_PORT_PAYLOAD("tcp", 522, NULL),   // Netmeeting
        NXT_MNT_END,
    },
};

NXT_DISSECTOR_INIT(tpkt)
{
    nxt_dissector_register(&gDissectorDef);

    // Register handoff rules for payload types
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "tpkt", 1, "q931");
    nxt_handoff_new_rule(NXT_HANDOFF_TYPE_NUMBER, "tpkt", 2, "h245");
}
