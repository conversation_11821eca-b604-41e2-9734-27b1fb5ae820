cmake_minimum_required(VERSION 3.14)

find_package(yaEngineNext REQUIRED)
# Include protocol configuration and setup ASN.1 protocols
include(${CMAKE_CURRENT_SOURCE_DIR}/asn1/protocols.cmake)
setup_asn1_protocols()

#
# plugins
#
addEngineNextPlugin(udp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_udp.c
)

addEngineNextPlugin(rtp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_rtp.c
)

addEngineNextPlugin(dns ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_dns.c
)

addEngineNextPlugin(sdx ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_sdx.c
)
addEngineNextPlugin(vlan ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_vlan.c
)

addEngineNextPlugin(gtp_u ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_gtp_u.c
)

addEngineNextPlugin(mpls ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_mpls.c
)

addEngineNextPlugin(smtp ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_smtp.c
  RAGEL_SOURCES parser_smtp.rl
)

addEngineNextPlugin(rt ${CMAKE_SOURCE_DIR}/bin/plugins
  TRAILER
  SOURCES dissector_trailer_rt.c
)

addEngineNextPlugin(sip ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES       dissector_sip.c
  RAGEL_SOURCES parser_sip.rl
)

addEngineNextPlugin(hwzzeth ${CMAKE_SOURCE_DIR}/bin/plugins
  TRAILER
  SOURCES dissector_hwzzeth.c
)

addEngineNextPlugin(trailer_hwzz ${CMAKE_SOURCE_DIR}/bin/plugins
  TRAILER
  SOURCES dissector_trailer_hwzz.c
)


# H.323 family fixed structure protocols
addEngineNextPlugin(tpkt ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_tpkt.c
)

addEngineNextPlugin(q931 ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES dissector_q931.c
)


# Automatically configure plugins for all ASN.1 protocols
configure_asn1_plugins()

# H.225 RAS protocol plugin (separate from h225 CS)
addEngineNextPlugin(h225ras ${CMAKE_SOURCE_DIR}/bin/plugins
  SOURCES asn1/protocols/h225/dissector_h225ras.c
  LINK_LIBRARIES "-Wl,--whole-archive" asn1_h225 "-Wl,--no-whole-archive"
)
add_dependencies(yaNxtDissector_h225ras asn1_build_h225)
