load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_h225ras.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_trailer_rt.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_rtp.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_q931.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_h245.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_smtp.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_mpls.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_vlan.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_story.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_h225.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_trailer_hwzzeth.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_sdx.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_dns.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_gtp_u.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_udp.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_snmp.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_trailer_trailer_hwzz.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_tpkt.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_h235.so
load plugin done: /home/<USER>/SDX/EngineNext/testyaEngineNext/bin/plugins/yaNxtDissector_sip.so
INFO: error: dissector [] not found.

offline mode: read pcap from file pcaps/H225_H245.pcap
dissector_h225ras
H225RAS: Attempting to decode 127 bytes of data
H225RAS: First few bytes: 26 90 5f 59
H225RAS: BER decode failed or invalid, trying PER decode
H225RAS: ASN.1 decode error: Decode failed - invalid ASN.1 structure (consumed: 0 bytes)
dissector_h225ras
H225RAS: Attempting to decode 24 bytes of data
H225RAS: First few bytes: 2b 00 5f 59
H225RAS: BER decode failed or invalid, trying PER decode
H225RAS: Successfully decoded RAS message
H225RAS: RAS message present value: 11
H225RAS: Decoded message type: admissionConfirm
{proto: "h225ras", teid: "", outer_sip: "", outer_dip: "", msisdn: "", imei: "", imsi: "", tac: "", plmn_id: "", uli: "", base_type: "", fixed_account: "", esn: "", apn: "", plmn_id: "", ecgi_mnc: "", lac: "", ci: "", sac: "", uli: "", ecgi: "", bsid: "", direction: "", ruleid: "", label: "", bs: "", bflag: "", operator: "", rat: "", teid: "", msisdn: "", imei: "", imsi: "", tac: "", PacketSN: "2", SessionSN: "2", RecordSN: "1", CaptureTime: "**********", CarriedTime: "", DissectTime: "**********", PacketLen: "66", EffectiveLen: "66", LinkType: "", ProtoLayers: "eth:ipv4:udp", ProtoFlags: "", C2S: "S2C", SrcIp: "***********", DstIp: "***********", SrcPort: "1719", DstPort: "58535", protocol: "h225ras", message_type: "admissionConfirm", data_length: "24", decode_status: "success", decode_error: "", request_seq_num: "24410", endpoint_identifier: "", gatekeeper_identifier: "", call_reference_value: "", bandwidth: "**********", reject_reason: "", disengage_reason: "", validation_error: "", asn1_decode_consumed: "190", }
Q.931 dissector called with 232 bytes
Q931: Found User-User IE at offset 39, length 193
Q931: Setting handoff to H.225 CS, User-User IE found
dissector_h225
H225: Processing CS message, data_len=196
H225: First few bytes: 7e 00 c1 05
H225: Found User-User IE, length=193
H225: H.225 protocol discriminator found, h225_len=192
H225: Attempting PER decode of 192 bytes
H225: H.225 data first few bytes: 20 a8 06 00
H225: Unaligned PER decode successful
H225: Successfully decoded H323_UserInformation
H225: Processing H323_UU_PDU
H225: H.245 tunnelling field not present
H225: No H.245 control data present
H225: H.323 message body present value: 1
